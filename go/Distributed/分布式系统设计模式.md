# 分布式系统设计模式

## 常见设计模式

### 1. 主从模式(Master-Slave)
**特点**：一个主节点，多个从节点
**应用**：MySQL主从复制、Redis主从

```go
type MasterSlaveCluster struct {
    master *Node
    slaves []*Node
    mutex  sync.RWMutex
}

func (c *MasterSlaveCluster) Write(data []byte) error {
    // 写操作只能在master上进行
    if err := c.master.Write(data); err != nil {
        return err
    }
    
    // 异步复制到slave
    go c.replicateToSlaves(data)
    return nil
}

func (c *MasterSlaveCluster) Read() ([]byte, error) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    // 读操作可以从slave读取
    if len(c.slaves) > 0 {
        slave := c.slaves[rand.Intn(len(c.slaves))]
        return slave.Read()
    }
    return c.master.Read()
}
```

### 2. 对等模式(Peer-to-Peer)
**特点**：所有节点地位相等
**应用**：BitTorrent、区块链

```go
type P2PNode struct {
    id    string
    peers map[string]*Peer
    data  map[string][]byte
}

func (n *P2PNode) BroadcastData(key string, data []byte) {
    n.data[key] = data
    
    for _, peer := range n.peers {
        go peer.SendData(key, data)
    }
}

func (n *P2PNode) RequestData(key string) []byte {
    if data, exists := n.data[key]; exists {
        return data
    }
    
    // 从其他节点请求数据
    for _, peer := range n.peers {
        if data := peer.RequestData(key); data != nil {
            n.data[key] = data
            return data
        }
    }
    return nil
}
```

### 3. 分片模式(Sharding)
**特点**：数据按规则分布到不同节点
**应用**：MongoDB分片、Redis Cluster

```go
type ShardedCluster struct {
    shards []Shard
    hasher hash.Hash
}

func (c *ShardedCluster) getShardIndex(key string) int {
    c.hasher.Reset()
    c.hasher.Write([]byte(key))
    return int(c.hasher.Sum32()) % len(c.shards)
}

func (c *ShardedCluster) Put(key string, value []byte) error {
    shardIndex := c.getShardIndex(key)
    return c.shards[shardIndex].Put(key, value)
}

func (c *ShardedCluster) Get(key string) ([]byte, error) {
    shardIndex := c.getShardIndex(key)
    return c.shards[shardIndex].Get(key)
}
```

## 一致性模式

### 1. 强一致性
**特点**：所有节点同时看到相同数据
**实现**：同步复制、分布式锁

```go
func (c *Cluster) StrongConsistentWrite(key string, value []byte) error {
    // 获取分布式锁
    lock, err := c.acquireDistributedLock(key)
    if err != nil {
        return err
    }
    defer lock.Release()
    
    // 同步写入所有节点
    var wg sync.WaitGroup
    errChan := make(chan error, len(c.nodes))
    
    for _, node := range c.nodes {
        wg.Add(1)
        go func(n *Node) {
            defer wg.Done()
            if err := n.Write(key, value); err != nil {
                errChan <- err
            }
        }(node)
    }
    
    wg.Wait()
    close(errChan)
    
    // 检查是否有错误
    for err := range errChan {
        if err != nil {
            return err
        }
    }
    
    return nil
}
```

### 2. 最终一致性
**特点**：系统最终会达到一致状态
**实现**：异步复制、向量时钟

```go
type EventuallyConsistentStore struct {
    localData map[string]VersionedValue
    peers     []*Peer
    vector    VectorClock
}

type VersionedValue struct {
    Value   []byte
    Version VectorClock
}

func (s *EventuallyConsistentStore) Put(key string, value []byte) {
    s.vector.Increment(s.nodeID)
    
    versionedValue := VersionedValue{
        Value:   value,
        Version: s.vector.Copy(),
    }
    
    s.localData[key] = versionedValue
    
    // 异步传播到其他节点
    go s.propagateToOthers(key, versionedValue)
}

func (s *EventuallyConsistentStore) resolveConflict(key string, incoming VersionedValue) {
    local, exists := s.localData[key]
    if !exists {
        s.localData[key] = incoming
        return
    }
    
    // 使用向量时钟解决冲突
    if incoming.Version.HappensBefore(local.Version) {
        // incoming更旧，忽略
        return
    } else if local.Version.HappensBefore(incoming.Version) {
        // incoming更新，接受
        s.localData[key] = incoming
    } else {
        // 并发冲突，需要应用层解决
        s.handleConcurrentConflict(key, local, incoming)
    }
}
```

## 容错模式

### 1. 故障检测
```go
type FailureDetector struct {
    nodes     map[string]*Node
    heartbeat time.Duration
    timeout   time.Duration
}

func (fd *FailureDetector) startMonitoring() {
    ticker := time.NewTicker(fd.heartbeat)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            fd.checkAllNodes()
        }
    }
}

func (fd *FailureDetector) checkAllNodes() {
    for nodeID, node := range fd.nodes {
        go func(id string, n *Node) {
            ctx, cancel := context.WithTimeout(context.Background(), fd.timeout)
            defer cancel()
            
            if err := n.Ping(ctx); err != nil {
                fd.handleNodeFailure(id)
            }
        }(nodeID, node)
    }
}
```

### 2. 故障恢复
```go
type FailoverManager struct {
    primary   *Node
    secondary []*Node
    detector  *FailureDetector
}

func (fm *FailoverManager) handlePrimaryFailure() {
    if len(fm.secondary) == 0 {
        log.Fatal("No secondary nodes available")
        return
    }
    
    // 选择新的primary
    newPrimary := fm.selectNewPrimary()
    
    // 数据同步
    if err := fm.syncData(newPrimary); err != nil {
        log.Printf("Data sync failed: %v", err)
        return
    }
    
    // 切换流量
    fm.primary = newPrimary
    fm.removeFromSecondary(newPrimary)
    
    log.Printf("Failover completed, new primary: %s", newPrimary.ID)
}
```

## 负载均衡模式

### 1. 轮询(Round Robin)
```go
type RoundRobinBalancer struct {
    servers []Server
    current int
    mutex   sync.Mutex
}

func (rb *RoundRobinBalancer) NextServer() Server {
    rb.mutex.Lock()
    defer rb.mutex.Unlock()
    
    server := rb.servers[rb.current]
    rb.current = (rb.current + 1) % len(rb.servers)
    return server
}
```

### 2. 一致性哈希
```go
type ConsistentHash struct {
    ring     map[uint32]string
    sortedKeys []uint32
    replicas int
}

func (ch *ConsistentHash) Add(node string) {
    for i := 0; i < ch.replicas; i++ {
        key := ch.hash(fmt.Sprintf("%s:%d", node, i))
        ch.ring[key] = node
        ch.sortedKeys = append(ch.sortedKeys, key)
    }
    sort.Slice(ch.sortedKeys, func(i, j int) bool {
        return ch.sortedKeys[i] < ch.sortedKeys[j]
    })
}

func (ch *ConsistentHash) Get(key string) string {
    if len(ch.ring) == 0 {
        return ""
    }
    
    hash := ch.hash(key)
    idx := sort.Search(len(ch.sortedKeys), func(i int) bool {
        return ch.sortedKeys[i] >= hash
    })
    
    if idx == len(ch.sortedKeys) {
        idx = 0
    }
    
    return ch.ring[ch.sortedKeys[idx]]
}
```

## 面试要点
1. **设计模式选择**：根据业务需求选择合适的架构模式
2. **一致性权衡**：CAP定理的理解和应用
3. **容错机制**：如何检测和处理节点故障
4. **负载均衡**：不同算法的优缺点和适用场景
5. **扩展性**：如何设计可水平扩展的系统
