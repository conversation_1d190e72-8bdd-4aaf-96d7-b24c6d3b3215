# Raft分布式共识算法

## 基本概念

Raft是一种易于理解的分布式共识算法，用于管理分布式系统中的状态一致性。

### 节点角色
- **Leader（领导者）**：处理客户端请求，负责日志复制
- **Follower（跟随者）**：被动接收Leader的日志条目
- **Candidate（候选者）**：Leader选举时的临时状态

### 核心特性
- **强一致性**：保证所有节点状态一致
- **容错性**：容忍少于半数节点故障
- **简单性**：相比Paxos更易理解和实现

## 核心机制

### 1. Leader选举
```go
type Raft struct {
    currentTerm int
    votedFor    int
    state       NodeState // Leader/Follower/Candidate
    log         []LogEntry
    commitIndex int
    lastApplied int
}

func (rf *Raft) startElection() {
    rf.mu.Lock()
    defer rf.mu.Unlock()

    rf.currentTerm++
    rf.state = Candidate
    rf.votedFor = rf.me

    voteCount := 1 // 自己的票

    for i := range rf.peers {
        if i != rf.me {
            go rf.sendRequestVote(i, &voteCount)
        }
    }
}
```

**选举流程**：
1. Follower超时未收到心跳，转为Candidate
2. 增加term，向其他节点请求投票
3. 获得多数票成为Leader，开始发送心跳

### 2. 日志复制
```go
type LogEntry struct {
    Term    int
    Index   int
    Command interface{}
}

func (rf *Raft) appendEntries(server int, args *AppendEntriesArgs) {
    reply := &AppendEntriesReply{}

    if rf.sendAppendEntries(server, args, reply) {
        rf.mu.Lock()
        defer rf.mu.Unlock()

        if reply.Success {
            rf.matchIndex[server] = args.PrevLogIndex + len(args.Entries)
            rf.nextIndex[server] = rf.matchIndex[server] + 1
            rf.updateCommitIndex()
        } else {
            rf.nextIndex[server]--
        }
    }
}
```

**复制流程**：
1. Leader接收客户端请求，追加到本地日志
2. 并行发送AppendEntries RPC到所有Follower
3. 收到多数确认后提交日志条目

### 3. 安全性保证

**Term机制**：
- 每次选举term递增
- 节点只能在每个term投票一次
- 高term的消息会被优先处理

**Leader完整性**：
- 只有拥有最新日志的节点才能成为Leader
- 通过比较lastLogIndex和lastLogTerm确定

### 4. 日志一致性

```go
func (rf *Raft) updateCommitIndex() {
    for n := rf.commitIndex + 1; n <= rf.getLastLogIndex(); n++ {
        count := 1 // Leader自己
        for i := range rf.peers {
            if i != rf.me && rf.matchIndex[i] >= n {
                count++
            }
        }

        if count > len(rf.peers)/2 && rf.log[n].Term == rf.currentTerm {
            rf.commitIndex = n
        }
    }
}
```

## 容错性分析

### 故障类型
- **节点崩溃**：Leader重新选举
- **网络分区**：多数派继续服务
- **消息丢失**：重试机制保证

### 容错能力
- **2f+1个节点**：最多容忍f个节点故障
- **例子**：5节点集群可容忍2个节点故障

## 性能优化

### 1. 批量处理
```go
func (rf *Raft) batchAppendEntries() {
    entries := make([]LogEntry, 0, batchSize)

    for len(entries) < batchSize {
        select {
        case entry := <-rf.entryChan:
            entries = append(entries, entry)
        case <-time.After(batchTimeout):
            break
        }
    }

    rf.replicateEntries(entries)
}
```

### 2. Pipeline复制
- 并行发送多个AppendEntries
- 减少网络往返时间
- 提高吞吐量

## 实际应用

### etcd
- Kubernetes的配置存储
- 使用Raft保证强一致性

### Consul
- 服务发现和配置管理
- Raft用于leader选举和数据复制

### TiKV
- 分布式KV存储
- Multi-Raft架构

## 面试要点
1. **三个角色**：Leader、Follower、Candidate的职责
2. **选举机制**：如何保证只有一个Leader
3. **日志复制**：如何保证数据一致性
4. **容错能力**：最多容忍多少节点故障
5. **与Paxos对比**：Raft的优势和劣势