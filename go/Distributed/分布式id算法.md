# 分布式ID生成算法

## 核心需求
- **全局唯一性**：分布式环境下ID不重复
- **高性能**：支持高并发生成
- **有序性**：趋势递增，便于数据库索引
- **高可用**：避免单点故障

## 主流方案对比

### 1. UUID
```go
import "github.com/google/uuid"

func generateUUID() string {
    return uuid.New().String()
}
```
- **优点**：本地生成，无依赖，全局唯一
- **缺点**：无序，长度大(36字符)，不适合主键

### 2. Snowflake算法
```
| 1位符号 | 41位时间戳 | 10位机器ID | 12位序列号 |
```

```go
type Snowflake struct {
    mutex     sync.Mutex
    timestamp int64
    machineID int64
    sequence  int64
}

func (s *Snowflake) Generate() int64 {
    s.mutex.Lock()
    defer s.mutex.Unlock()

    now := time.Now().UnixNano() / 1000000
    if now == s.timestamp {
        s.sequence = (s.sequence + 1) & 4095
        if s.sequence == 0 {
            // 等待下一毫秒
            for now <= s.timestamp {
                now = time.Now().UnixNano() / 1000000
            }
        }
    } else {
        s.sequence = 0
    }

    s.timestamp = now
    return ((now - 1609459200000) << 22) | (s.machineID << 12) | s.sequence
}
```

**时钟回拨问题解决**：
- 检测时钟回拨，拒绝生成ID
- 使用本地时钟偏移量
- 预分配时间段

### 3. 数据库自增
```sql
CREATE TABLE id_generator (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stub CHAR(1) NOT NULL DEFAULT ''
) ENGINE=MyISAM;

INSERT INTO id_generator (stub) VALUES ('a');
SELECT LAST_INSERT_ID();
```
- **优点**：简单可靠，强一致性
- **缺点**：性能瓶颈，单点故障

### 4. 号段模式
```go
type SegmentIDGenerator struct {
    currentID int64
    maxID     int64
    step      int64
    mutex     sync.Mutex
}

func (g *SegmentIDGenerator) NextID() (int64, error) {
    g.mutex.Lock()
    defer g.mutex.Unlock()

    if g.currentID >= g.maxID {
        if err := g.loadNextSegment(); err != nil {
            return 0, err
        }
    }

    g.currentID++
    return g.currentID, nil
}
```

### 5. Redis方案
```go
func generateIDFromRedis(key string) (int64, error) {
    return rdb.Incr(ctx, key).Result()
}

// 带过期时间的ID生成
func generateDailyID() (int64, error) {
    key := "id:" + time.Now().Format("2006-01-02")
    id, err := rdb.Incr(ctx, key).Result()
    if err != nil {
        return 0, err
    }

    // 设置过期时间
    rdb.Expire(ctx, key, 25*time.Hour)
    return id, nil
}
```

## 方案选择指南

| 方案 | 性能 | 复杂度 | 有序性 | 依赖 | 适用场景 |
|------|------|--------|--------|------|----------|
| UUID | 高 | 低 | 无 | 无 | 对有序性无要求 |
| Snowflake | 高 | 中 | 有 | 时钟 | 高并发，需要有序 |
| 数据库自增 | 低 | 低 | 有 | 数据库 | 小规模系统 |
| 号段模式 | 高 | 中 | 有 | 数据库 | 中等规模系统 |
| Redis | 高 | 低 | 有 | Redis | 简单快速实现 |

## 面试要点
1. **Snowflake原理**：时间戳+机器ID+序列号的组合
2. **时钟回拨**：如何检测和处理时钟回拨问题
3. **机器ID分配**：如何保证机器ID不冲突
4. **性能对比**：各方案的优缺点和适用场景
5. **高可用设计**：如何避免单点故障