Redis集群架构是构建高可用、高性能缓存系统的关键技术。理解Redis集群的工作原理、部署策略和故障处理机制对于设计大规模分布式系统至关重要。

### 1. **Redis集群架构概述**

#### **Redis集群的特点**
- **数据分片**：自动将数据分布到多个节点
- **高可用性**：支持主从复制和自动故障转移
- **水平扩展**：可以动态添加或删除节点
- **无中心化**：所有节点地位平等，没有单点故障

#### **集群拓扑结构**
```
Redis Cluster 拓扑：
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Master1   │    │   Master2   │    │   Master3   │
│  (0-5460)   │    │ (5461-10922)│    │(10923-16383)│
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Slave1    │    │   Slave2    │    │   Slave3    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 2. **Go语言Redis集群客户端实现**

#### **基础集群客户端**
```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/go-redis/redis/v8"
)

type RedisClusterClient struct {
    client *redis.ClusterClient
    config *ClusterConfig
}

type ClusterConfig struct {
    Addrs              []string      `json:"addrs"`
    Password           string        `json:"password"`
    MaxRetries         int           `json:"max_retries"`
    DialTimeout        time.Duration `json:"dial_timeout"`
    ReadTimeout        time.Duration `json:"read_timeout"`
    WriteTimeout       time.Duration `json:"write_timeout"`
    PoolSize           int           `json:"pool_size"`
    MinIdleConns       int           `json:"min_idle_conns"`
    MaxConnAge         time.Duration `json:"max_conn_age"`
    PoolTimeout        time.Duration `json:"pool_timeout"`
    IdleTimeout        time.Duration `json:"idle_timeout"`
    IdleCheckFrequency time.Duration `json:"idle_check_frequency"`
}

func NewRedisClusterClient(config *ClusterConfig) *RedisClusterClient {
    rdb := redis.NewClusterClient(&redis.ClusterOptions{
        Addrs:              config.Addrs,
        Password:           config.Password,
        MaxRetries:         config.MaxRetries,
        DialTimeout:        config.DialTimeout,
        ReadTimeout:        config.ReadTimeout,
        WriteTimeout:       config.WriteTimeout,
        PoolSize:           config.PoolSize,
        MinIdleConns:       config.MinIdleConns,
        MaxConnAge:         config.MaxConnAge,
        PoolTimeout:        config.PoolTimeout,
        IdleTimeout:        config.IdleTimeout,
        IdleCheckFrequency: config.IdleCheckFrequency,
        
        // 集群特定配置
        RouteByLatency:  true,  // 根据延迟路由
        RouteRandomly:   false, // 不随机路由
        ClusterSlots:    nil,   // 自动发现槽位
    })
    
    return &RedisClusterClient{
        client: rdb,
        config: config,
    }
}

// 健康检查
func (rcc *RedisClusterClient) HealthCheck(ctx context.Context) error {
    // 检查集群状态
    clusterInfo, err := rcc.client.ClusterInfo(ctx).Result()
    if err != nil {
        return fmt.Errorf("cluster info failed: %w", err)
    }
    
    if !strings.Contains(clusterInfo, "cluster_state:ok") {
        return fmt.Errorf("cluster state is not ok: %s", clusterInfo)
    }
    
    // 检查所有节点
    nodes, err := rcc.client.ClusterNodes(ctx).Result()
    if err != nil {
        return fmt.Errorf("cluster nodes failed: %w", err)
    }
    
    return rcc.validateNodes(nodes)
}

func (rcc *RedisClusterClient) validateNodes(nodesInfo string) error {
    lines := strings.Split(nodesInfo, "\n")
    masterCount := 0
    
    for _, line := range lines {
        if line == "" {
            continue
        }
        
        parts := strings.Fields(line)
        if len(parts) < 3 {
            continue
        }
        
        flags := parts[2]
        if strings.Contains(flags, "master") {
            masterCount++
            if strings.Contains(flags, "fail") {
                return fmt.Errorf("master node failed: %s", parts[0])
            }
        }
    }
    
    if masterCount < 3 {
        return fmt.Errorf("insufficient master nodes: %d", masterCount)
    }
    
    return nil
}
```

#### **智能路由和负载均衡**
```go
type SmartClusterClient struct {
    *RedisClusterClient
    nodeLatency map[string]time.Duration
    mu          sync.RWMutex
}

func NewSmartClusterClient(config *ClusterConfig) *SmartClusterClient {
    base := NewRedisClusterClient(config)
    
    smart := &SmartClusterClient{
        RedisClusterClient: base,
        nodeLatency:       make(map[string]time.Duration),
    }
    
    // 启动延迟监控
    go smart.monitorLatency()
    
    return smart
}

func (scc *SmartClusterClient) monitorLatency() {
    ticker := time.NewTicker(time.Second * 30)
    defer ticker.Stop()
    
    for range ticker.C {
        scc.measureNodeLatency()
    }
}

func (scc *SmartClusterClient) measureNodeLatency() {
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
    defer cancel()
    
    // 获取所有节点信息
    nodes, err := scc.client.ClusterNodes(ctx).Result()
    if err != nil {
        return
    }
    
    scc.mu.Lock()
    defer scc.mu.Unlock()
    
    lines := strings.Split(nodes, "\n")
    for _, line := range lines {
        if line == "" {
            continue
        }
        
        parts := strings.Fields(line)
        if len(parts) < 2 {
            continue
        }
        
        nodeAddr := parts[1]
        if strings.Contains(nodeAddr, "@") {
            nodeAddr = strings.Split(nodeAddr, "@")[0]
        }
        
        // 测量延迟
        start := time.Now()
        err := scc.client.Ping(ctx).Err()
        if err == nil {
            scc.nodeLatency[nodeAddr] = time.Since(start)
        }
    }
}

// 基于延迟的读取操作
func (scc *SmartClusterClient) SmartGet(ctx context.Context, key string) (string, error) {
    // 获取key对应的槽位
    slot := redis.HashSlot(key)
    
    // 获取该槽位的节点信息
    nodes := scc.getSlotNodes(slot)
    if len(nodes) == 0 {
        return scc.client.Get(ctx, key).Result()
    }
    
    // 选择延迟最低的从节点进行读取
    bestNode := scc.selectBestReadNode(nodes)
    if bestNode != "" {
        // 直接连接到最佳节点
        nodeClient := redis.NewClient(&redis.Options{
            Addr: bestNode,
        })
        defer nodeClient.Close()
        
        return nodeClient.Get(ctx, key).Result()
    }
    
    // 回退到默认行为
    return scc.client.Get(ctx, key).Result()
}

func (scc *SmartClusterClient) selectBestReadNode(nodes []string) string {
    scc.mu.RLock()
    defer scc.mu.RUnlock()
    
    var bestNode string
    var minLatency time.Duration = time.Hour
    
    for _, node := range nodes {
        if latency, exists := scc.nodeLatency[node]; exists {
            if latency < minLatency {
                minLatency = latency
                bestNode = node
            }
        }
    }
    
    return bestNode
}
```

### 3. **集群数据分片策略**

#### **一致性哈希实现**
```go
type ConsistentHash struct {
    ring     map[uint32]string
    sortedKeys []uint32
    nodes    map[string]bool
    replicas int
    mu       sync.RWMutex
}

func NewConsistentHash(replicas int) *ConsistentHash {
    return &ConsistentHash{
        ring:     make(map[uint32]string),
        nodes:    make(map[string]bool),
        replicas: replicas,
    }
}

func (ch *ConsistentHash) AddNode(node string) {
    ch.mu.Lock()
    defer ch.mu.Unlock()
    
    if ch.nodes[node] {
        return
    }
    
    ch.nodes[node] = true
    
    // 为每个节点创建多个虚拟节点
    for i := 0; i < ch.replicas; i++ {
        virtualKey := fmt.Sprintf("%s:%d", node, i)
        hash := ch.hashKey(virtualKey)
        ch.ring[hash] = node
        ch.sortedKeys = append(ch.sortedKeys, hash)
    }
    
    sort.Slice(ch.sortedKeys, func(i, j int) bool {
        return ch.sortedKeys[i] < ch.sortedKeys[j]
    })
}

func (ch *ConsistentHash) RemoveNode(node string) {
    ch.mu.Lock()
    defer ch.mu.Unlock()
    
    if !ch.nodes[node] {
        return
    }
    
    delete(ch.nodes, node)
    
    // 移除虚拟节点
    for i := 0; i < ch.replicas; i++ {
        virtualKey := fmt.Sprintf("%s:%d", node, i)
        hash := ch.hashKey(virtualKey)
        delete(ch.ring, hash)
        
        // 从排序数组中移除
        for j, key := range ch.sortedKeys {
            if key == hash {
                ch.sortedKeys = append(ch.sortedKeys[:j], ch.sortedKeys[j+1:]...)
                break
            }
        }
    }
}

func (ch *ConsistentHash) GetNode(key string) string {
    ch.mu.RLock()
    defer ch.mu.RUnlock()
    
    if len(ch.sortedKeys) == 0 {
        return ""
    }
    
    hash := ch.hashKey(key)
    
    // 找到第一个大于等于hash的节点
    idx := sort.Search(len(ch.sortedKeys), func(i int) bool {
        return ch.sortedKeys[i] >= hash
    })
    
    // 如果没找到，使用第一个节点（环形结构）
    if idx == len(ch.sortedKeys) {
        idx = 0
    }
    
    return ch.ring[ch.sortedKeys[idx]]
}

func (ch *ConsistentHash) hashKey(key string) uint32 {
    h := fnv.New32a()
    h.Write([]byte(key))
    return h.Sum32()
}
```

#### **数据迁移管理**
```go
type DataMigrationManager struct {
    sourceClient *redis.Client
    targetClient *redis.Client
    batchSize    int
    concurrency  int
}

func NewDataMigrationManager(source, target *redis.Client) *DataMigrationManager {
    return &DataMigrationManager{
        sourceClient: source,
        targetClient: target,
        batchSize:    1000,
        concurrency:  10,
    }
}

func (dmm *DataMigrationManager) MigrateSlot(ctx context.Context, slot int) error {
    // 获取槽位中的所有key
    keys, err := dmm.getSlotKeys(ctx, slot)
    if err != nil {
        return fmt.Errorf("failed to get slot keys: %w", err)
    }
    
    // 分批迁移
    batches := dmm.createBatches(keys)
    
    // 并发迁移
    semaphore := make(chan struct{}, dmm.concurrency)
    var wg sync.WaitGroup
    var migrationErr error
    
    for _, batch := range batches {
        wg.Add(1)
        go func(keyBatch []string) {
            defer wg.Done()
            
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            if err := dmm.migrateBatch(ctx, keyBatch); err != nil {
                migrationErr = err
            }
        }(batch)
    }
    
    wg.Wait()
    
    if migrationErr != nil {
        return migrationErr
    }
    
    // 验证迁移结果
    return dmm.verifyMigration(ctx, keys)
}

func (dmm *DataMigrationManager) migrateBatch(ctx context.Context, keys []string) error {
    pipe := dmm.sourceClient.Pipeline()
    
    // 批量获取数据
    for _, key := range keys {
        pipe.Dump(ctx, key)
        pipe.TTL(ctx, key)
    }
    
    results, err := pipe.Exec(ctx)
    if err != nil {
        return err
    }
    
    // 批量写入目标
    targetPipe := dmm.targetClient.Pipeline()
    
    for i := 0; i < len(keys); i++ {
        dumpResult := results[i*2].(*redis.StringCmd)
        ttlResult := results[i*2+1].(*redis.DurationCmd)
        
        if dumpResult.Err() != nil {
            continue
        }
        
        data, _ := dumpResult.Result()
        ttl, _ := ttlResult.Result()
        
        if ttl < 0 {
            ttl = 0
        }
        
        targetPipe.Restore(ctx, keys[i], ttl, data)
    }
    
    _, err = targetPipe.Exec(ctx)
    return err
}

func (dmm *DataMigrationManager) createBatches(keys []string) [][]string {
    var batches [][]string
    
    for i := 0; i < len(keys); i += dmm.batchSize {
        end := i + dmm.batchSize
        if end > len(keys) {
            end = len(keys)
        }
        batches = append(batches, keys[i:end])
    }
    
    return batches
}
```

### 4. **故障检测和自动恢复**

#### **节点健康监控**
```go
type ClusterMonitor struct {
    client       *redis.ClusterClient
    nodeStatus   map[string]NodeStatus
    alertChannel chan Alert
    mu           sync.RWMutex
}

type NodeStatus struct {
    Address     string        `json:"address"`
    Role        string        `json:"role"`
    Status      string        `json:"status"`
    LastCheck   time.Time     `json:"last_check"`
    Latency     time.Duration `json:"latency"`
    Memory      int64         `json:"memory"`
    Connections int           `json:"connections"`
}

type Alert struct {
    Type      string    `json:"type"`
    Node      string    `json:"node"`
    Message   string    `json:"message"`
    Timestamp time.Time `json:"timestamp"`
    Severity  string    `json:"severity"`
}

func NewClusterMonitor(client *redis.ClusterClient) *ClusterMonitor {
    monitor := &ClusterMonitor{
        client:       client,
        nodeStatus:   make(map[string]NodeStatus),
        alertChannel: make(chan Alert, 100),
    }
    
    go monitor.startMonitoring()
    go monitor.handleAlerts()
    
    return monitor
}

func (cm *ClusterMonitor) startMonitoring() {
    ticker := time.NewTicker(time.Second * 10)
    defer ticker.Stop()
    
    for range ticker.C {
        cm.checkClusterHealth()
    }
}

func (cm *ClusterMonitor) checkClusterHealth() {
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
    defer cancel()
    
    // 获取集群节点信息
    nodes, err := cm.client.ClusterNodes(ctx).Result()
    if err != nil {
        cm.sendAlert("CLUSTER_ERROR", "", fmt.Sprintf("Failed to get cluster nodes: %v", err), "HIGH")
        return
    }
    
    cm.parseAndCheckNodes(ctx, nodes)
}

func (cm *ClusterMonitor) parseAndCheckNodes(ctx context.Context, nodesInfo string) {
    lines := strings.Split(nodesInfo, "\n")
    
    for _, line := range lines {
        if line == "" {
            continue
        }
        
        parts := strings.Fields(line)
        if len(parts) < 8 {
            continue
        }
        
        nodeID := parts[0]
        address := strings.Split(parts[1], "@")[0]
        flags := parts[2]
        
        status := cm.checkNodeStatus(ctx, address, flags)
        
        cm.mu.Lock()
        cm.nodeStatus[nodeID] = status
        cm.mu.Unlock()
        
        // 检查是否需要发送告警
        cm.evaluateNodeHealth(status)
    }
}

func (cm *ClusterMonitor) checkNodeStatus(ctx context.Context, address, flags string) NodeStatus {
    start := time.Now()
    
    // 创建单独的客户端连接到特定节点
    nodeClient := redis.NewClient(&redis.Options{
        Addr:        address,
        DialTimeout: time.Second * 2,
        ReadTimeout: time.Second * 2,
    })
    defer nodeClient.Close()
    
    status := NodeStatus{
        Address:   address,
        LastCheck: time.Now(),
        Status:    "DOWN",
    }
    
    // 检查连接
    if err := nodeClient.Ping(ctx).Err(); err != nil {
        return status
    }
    
    status.Status = "UP"
    status.Latency = time.Since(start)
    
    // 获取角色信息
    if strings.Contains(flags, "master") {
        status.Role = "MASTER"
    } else if strings.Contains(flags, "slave") {
        status.Role = "SLAVE"
    }
    
    // 获取内存使用情况
    if info, err := nodeClient.Info(ctx, "memory").Result(); err == nil {
        status.Memory = cm.parseMemoryUsage(info)
    }
    
    // 获取连接数
    if info, err := nodeClient.Info(ctx, "clients").Result(); err == nil {
        status.Connections = cm.parseConnectedClients(info)
    }
    
    return status
}

func (cm *ClusterMonitor) evaluateNodeHealth(status NodeStatus) {
    // 检查延迟
    if status.Latency > time.Millisecond*100 {
        cm.sendAlert("HIGH_LATENCY", status.Address, 
            fmt.Sprintf("Node latency: %v", status.Latency), "MEDIUM")
    }
    
    // 检查内存使用
    if status.Memory > 1024*1024*1024*8 { // 8GB
        cm.sendAlert("HIGH_MEMORY", status.Address,
            fmt.Sprintf("Memory usage: %d bytes", status.Memory), "MEDIUM")
    }
    
    // 检查连接数
    if status.Connections > 10000 {
        cm.sendAlert("HIGH_CONNECTIONS", status.Address,
            fmt.Sprintf("Connection count: %d", status.Connections), "LOW")
    }
    
    // 检查节点状态
    if status.Status == "DOWN" {
        cm.sendAlert("NODE_DOWN", status.Address, "Node is unreachable", "HIGH")
    }
}

func (cm *ClusterMonitor) sendAlert(alertType, node, message, severity string) {
    alert := Alert{
        Type:      alertType,
        Node:      node,
        Message:   message,
        Timestamp: time.Now(),
        Severity:  severity,
    }
    
    select {
    case cm.alertChannel <- alert:
    default:
        // 告警通道满了，丢弃告警
    }
}
```

### 5. **集群扩容和缩容**

#### **动态扩容实现**
```go
type ClusterScaler struct {
    client  *redis.ClusterClient
    monitor *ClusterMonitor
}

func NewClusterScaler(client *redis.ClusterClient, monitor *ClusterMonitor) *ClusterScaler {
    return &ClusterScaler{
        client:  client,
        monitor: monitor,
    }
}

func (cs *ClusterScaler) ScaleOut(newNodes []string) error {
    for _, node := range newNodes {
        if err := cs.addNode(node); err != nil {
            return fmt.Errorf("failed to add node %s: %w", node, err)
        }
    }
    
    // 重新分配槽位
    return cs.rebalanceSlots()
}

func (cs *ClusterScaler) addNode(nodeAddr string) error {
    ctx := context.Background()
    
    // 连接到新节点
    newNodeClient := redis.NewClient(&redis.Options{
        Addr: nodeAddr,
    })
    defer newNodeClient.Close()
    
    // 重置节点状态
    if err := newNodeClient.ClusterReset(ctx, "hard").Err(); err != nil {
        return err
    }
    
    // 获取现有集群中的一个节点
    existingNode, err := cs.getExistingNode()
    if err != nil {
        return err
    }
    
    // 将新节点加入集群
    return existingNode.ClusterMeet(ctx, parseHost(nodeAddr), parsePort(nodeAddr)).Err()
}

func (cs *ClusterScaler) rebalanceSlots() error {
    ctx := context.Background()
    
    // 获取当前槽位分配
    slots, err := cs.getCurrentSlotAllocation()
    if err != nil {
        return err
    }
    
    // 计算新的槽位分配
    newAllocation := cs.calculateNewAllocation(slots)
    
    // 执行槽位迁移
    return cs.migrateSlots(ctx, newAllocation)
}

func (cs *ClusterScaler) calculateNewAllocation(currentSlots map[string][]int) map[string][]int {
    // 获取所有主节点
    masters := cs.getMasterNodes()
    totalSlots := 16384
    slotsPerNode := totalSlots / len(masters)
    
    newAllocation := make(map[string][]int)
    slotIndex := 0
    
    for i, master := range masters {
        startSlot := i * slotsPerNode
        endSlot := startSlot + slotsPerNode
        
        if i == len(masters)-1 {
            endSlot = totalSlots // 最后一个节点分配剩余所有槽位
        }
        
        var slots []int
        for j := startSlot; j < endSlot; j++ {
            slots = append(slots, j)
        }
        
        newAllocation[master] = slots
    }
    
    return newAllocation
}
```

### 6. **面试常见问题**

#### **问题1：Redis集群的数据分片原理**
- **哈希槽**：16384个槽位，使用CRC16算法计算key的槽位
- **槽位分配**：每个主节点负责一部分槽位
- **数据路由**：客户端根据key计算槽位，直接访问对应节点

#### **问题2：Redis集群的故障转移机制**
- **故障检测**：节点间互相ping检测
- **主观下线**：单个节点认为某节点故障
- **客观下线**：大多数节点认为某节点故障
- **故障转移**：从节点自动提升为主节点

#### **问题3：Redis集群vs Redis Sentinel的区别**
- **Sentinel**：主从架构，单点写入，适合读多写少
- **Cluster**：分片架构，多点写入，适合大数据量
- **复杂度**：Cluster更复杂，Sentinel更简单
- **扩展性**：Cluster支持水平扩展，Sentinel垂直扩展

### 总结

Redis集群架构设计需要考虑：

1. **数据分片策略**：合理的槽位分配和数据分布
2. **高可用保证**：主从复制和自动故障转移
3. **性能优化**：智能路由和负载均衡
4. **运维管理**：监控告警和自动化运维
5. **扩展能力**：支持动态扩容和缩容

在实际应用中，需要根据业务特点选择合适的集群方案，并建立完善的监控和运维体系，确保集群的稳定性和高可用性。
