# Linux系统编程与性能调优实战

## 1. 系统调用深度解析

### 1.1 系统调用机制

```go
package main

import (
    "fmt"
    "os"
    "syscall"
    "unsafe"
)

// 演示系统调用的使用
func demonstrateSystemCalls() {
    // 1. 文件操作系统调用
    fd, err := syscall.Open("/tmp/test.txt", syscall.O_CREAT|syscall.O_WRONLY, 0644)
    if err != nil {
        fmt.Printf("Open failed: %v\n", err)
        return
    }
    defer syscall.Close(fd)
    
    // 2. 写入数据
    data := []byte("Hello, System Call!")
    n, err := syscall.Write(fd, data)
    if err != nil {
        fmt.Printf("Write failed: %v\n", err)
        return
    }
    fmt.Printf("Written %d bytes\n", n)
    
    // 3. 获取文件信息
    var stat syscall.Stat_t
    err = syscall.Fstat(fd, &stat)
    if err != nil {
        fmt.Printf("Fstat failed: %v\n", err)
        return
    }
    fmt.Printf("File size: %d bytes\n", stat.Size)
    fmt.Printf("File mode: %o\n", stat.Mode)
    fmt.Printf("Inode: %d\n", stat.Ino)
}

// 演示内存映射
func demonstrateMmap() {
    // 创建一个文件
    file, err := os.Create("/tmp/mmap_test.txt")
    if err != nil {
        panic(err)
    }
    defer file.Close()
    defer os.Remove("/tmp/mmap_test.txt")
    
    // 写入一些数据
    data := make([]byte, 4096)
    for i := range data {
        data[i] = byte(i % 256)
    }
    file.Write(data)
    
    // 内存映射
    fd := int(file.Fd())
    addr, err := syscall.Mmap(fd, 0, len(data), syscall.PROT_READ|syscall.PROT_WRITE, syscall.MAP_SHARED)
    if err != nil {
        panic(err)
    }
    defer syscall.Munmap(addr)
    
    // 通过内存映射修改文件
    addr[0] = 0xFF
    addr[1] = 0xFE
    
    // 同步到磁盘
    err = syscall.Msync(addr, syscall.MS_SYNC)
    if err != nil {
        fmt.Printf("Msync failed: %v\n", err)
    }
    
    fmt.Printf("Memory mapped file modified: first bytes = %02x %02x\n", addr[0], addr[1])
}
```

### 1.2 进程管理系统调用

```go
import (
    "os"
    "os/exec"
    "syscall"
    "time"
)

// 演示进程创建和管理
func demonstrateProcessManagement() {
    // 1. 创建子进程
    cmd := exec.Command("sleep", "5")
    cmd.Start()
    
    pid := cmd.Process.Pid
    fmt.Printf("Created child process with PID: %d\n", pid)
    
    // 2. 发送信号
    go func() {
        time.Sleep(2 * time.Second)
        fmt.Printf("Sending SIGTERM to process %d\n", pid)
        cmd.Process.Signal(syscall.SIGTERM)
    }()
    
    // 3. 等待进程结束
    state, err := cmd.Process.Wait()
    if err != nil {
        fmt.Printf("Wait failed: %v\n", err)
        return
    }
    
    fmt.Printf("Process exited with status: %v\n", state)
    
    // 4. 获取进程资源使用情况
    var rusage syscall.Rusage
    err = syscall.Getrusage(syscall.RUSAGE_CHILDREN, &rusage)
    if err == nil {
        fmt.Printf("Child processes used %d.%06d seconds of user time\n",
            rusage.Utime.Sec, rusage.Utime.Usec)
        fmt.Printf("Child processes used %d.%06d seconds of system time\n",
            rusage.Stime.Sec, rusage.Stime.Usec)
        fmt.Printf("Maximum resident set size: %d KB\n", rusage.Maxrss)
    }
}

// 演示信号处理
func demonstrateSignalHandling() {
    // 设置信号处理器
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGUSR1)
    
    fmt.Println("Signal handler installed. Send signals to test:")
    fmt.Printf("PID: %d\n", os.Getpid())
    fmt.Println("SIGINT (Ctrl+C): Graceful shutdown")
    fmt.Println("SIGTERM: Immediate shutdown")
    fmt.Println("SIGUSR1: Print status")
    
    for {
        sig := <-sigChan
        switch sig {
        case syscall.SIGINT:
            fmt.Println("Received SIGINT, shutting down gracefully...")
            time.Sleep(time.Second) // 模拟清理工作
            return
        case syscall.SIGTERM:
            fmt.Println("Received SIGTERM, shutting down immediately...")
            return
        case syscall.SIGUSR1:
            fmt.Printf("Status: PID=%d, Goroutines=%d\n", 
                os.Getpid(), runtime.NumGoroutine())
        }
    }
}
```

## 2. I/O多路复用深度实现

### 2.1 Epoll实现高性能服务器

```go
import (
    "net"
    "syscall"
    "unsafe"
)

// Epoll封装
type EpollServer struct {
    epfd     int
    listener net.Listener
    events   []syscall.EpollEvent
}

func NewEpollServer(addr string) (*EpollServer, error) {
    // 创建监听socket
    listener, err := net.Listen("tcp", addr)
    if err != nil {
        return nil, err
    }
    
    // 创建epoll实例
    epfd, err := syscall.EpollCreate1(0)
    if err != nil {
        listener.Close()
        return nil, err
    }
    
    // 获取监听socket的文件描述符
    file, err := listener.(*net.TCPListener).File()
    if err != nil {
        syscall.Close(epfd)
        listener.Close()
        return nil, err
    }
    
    listenerFd := int(file.Fd())
    
    // 将监听socket添加到epoll
    event := syscall.EpollEvent{
        Events: syscall.EPOLLIN,
        Fd:     int32(listenerFd),
    }
    
    err = syscall.EpollCtl(epfd, syscall.EPOLL_CTL_ADD, listenerFd, &event)
    if err != nil {
        syscall.Close(epfd)
        listener.Close()
        return nil, err
    }
    
    return &EpollServer{
        epfd:     epfd,
        listener: listener,
        events:   make([]syscall.EpollEvent, 128),
    }, nil
}

func (es *EpollServer) Run() error {
    defer syscall.Close(es.epfd)
    defer es.listener.Close()
    
    listenerFile, _ := es.listener.(*net.TCPListener).File()
    listenerFd := int(listenerFile.Fd())
    
    fmt.Printf("Epoll server listening on %s\n", es.listener.Addr())
    
    for {
        // 等待事件
        nfds, err := syscall.EpollWait(es.epfd, es.events, -1)
        if err != nil {
            if err == syscall.EINTR {
                continue
            }
            return err
        }
        
        // 处理事件
        for i := 0; i < nfds; i++ {
            event := es.events[i]
            fd := int(event.Fd)
            
            if fd == listenerFd {
                // 新连接
                es.handleNewConnection()
            } else {
                // 数据可读
                es.handleClientData(fd)
            }
        }
    }
}

func (es *EpollServer) handleNewConnection() {
    conn, err := es.listener.Accept()
    if err != nil {
        fmt.Printf("Accept failed: %v\n", err)
        return
    }
    
    // 设置非阻塞
    file, err := conn.(*net.TCPConn).File()
    if err != nil {
        conn.Close()
        return
    }
    
    fd := int(file.Fd())
    err = syscall.SetNonblock(fd, true)
    if err != nil {
        conn.Close()
        return
    }
    
    // 添加到epoll
    event := syscall.EpollEvent{
        Events: syscall.EPOLLIN | syscall.EPOLLET, // 边缘触发
        Fd:     int32(fd),
    }
    
    err = syscall.EpollCtl(es.epfd, syscall.EPOLL_CTL_ADD, fd, &event)
    if err != nil {
        conn.Close()
        return
    }
    
    fmt.Printf("New connection: fd=%d\n", fd)
}

func (es *EpollServer) handleClientData(fd int) {
    buffer := make([]byte, 4096)
    
    for {
        n, err := syscall.Read(fd, buffer)
        if err != nil {
            if err == syscall.EAGAIN || err == syscall.EWOULDBLOCK {
                // 没有更多数据
                break
            }
            // 连接错误，关闭连接
            es.closeConnection(fd)
            return
        }
        
        if n == 0 {
            // 连接关闭
            es.closeConnection(fd)
            return
        }
        
        // 回显数据
        syscall.Write(fd, buffer[:n])
        
        // 边缘触发模式下需要读取所有可用数据
        if n < len(buffer) {
            break
        }
    }
}

func (es *EpollServer) closeConnection(fd int) {
    syscall.EpollCtl(es.epfd, syscall.EPOLL_CTL_DEL, fd, nil)
    syscall.Close(fd)
    fmt.Printf("Connection closed: fd=%d\n", fd)
}
```

### 2.2 零拷贝技术实现

```go
// 使用sendfile实现零拷贝文件传输
func sendFileZeroCopy(conn net.Conn, filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer file.Close()
    
    // 获取文件大小
    stat, err := file.Stat()
    if err != nil {
        return err
    }
    
    // 获取连接的文件描述符
    tcpConn := conn.(*net.TCPConn)
    connFile, err := tcpConn.File()
    if err != nil {
        return err
    }
    defer connFile.Close()
    
    // 使用sendfile进行零拷贝传输
    var offset int64 = 0
    remaining := stat.Size()
    
    for remaining > 0 {
        n, err := syscall.Sendfile(int(connFile.Fd()), int(file.Fd()), &offset, int(remaining))
        if err != nil {
            return err
        }
        remaining -= int64(n)
    }
    
    return nil
}

// 使用splice实现管道零拷贝
func spliceZeroCopy(src, dst int, size int) error {
    // 创建管道
    pipeRead, pipeWrite, err := os.Pipe()
    if err != nil {
        return err
    }
    defer pipeRead.Close()
    defer pipeWrite.Close()
    
    remaining := size
    
    for remaining > 0 {
        // 从源文件描述符splice到管道
        n1, err := syscall.Splice(src, nil, int(pipeWrite.Fd()), nil, remaining, 0)
        if err != nil {
            return err
        }
        
        // 从管道splice到目标文件描述符
        n2, err := syscall.Splice(int(pipeRead.Fd()), nil, dst, nil, int(n1), 0)
        if err != nil {
            return err
        }
        
        remaining -= int(n2)
    }
    
    return nil
}
```

## 3. 内存管理与优化

### 3.1 内存分配器实现

```go
// 简化的内存池实现
type MemoryPool struct {
    pools map[int]*sync.Pool
    sizes []int
}

func NewMemoryPool() *MemoryPool {
    sizes := []int{64, 128, 256, 512, 1024, 2048, 4096, 8192}
    pools := make(map[int]*sync.Pool)
    
    for _, size := range sizes {
        size := size // 捕获循环变量
        pools[size] = &sync.Pool{
            New: func() interface{} {
                return make([]byte, size)
            },
        }
    }
    
    return &MemoryPool{
        pools: pools,
        sizes: sizes,
    }
}

func (mp *MemoryPool) Get(size int) []byte {
    // 找到合适的池
    for _, poolSize := range mp.sizes {
        if size <= poolSize {
            buf := mp.pools[poolSize].Get().([]byte)
            return buf[:size]
        }
    }
    
    // 如果没有合适的池，直接分配
    return make([]byte, size)
}

func (mp *MemoryPool) Put(buf []byte) {
    size := cap(buf)
    if pool, exists := mp.pools[size]; exists {
        pool.Put(buf[:cap(buf)])
    }
}

// 内存使用情况监控
func monitorMemoryUsage() {
    var m runtime.MemStats
    
    ticker := time.NewTicker(time.Second * 5)
    defer ticker.Stop()
    
    for range ticker.C {
        runtime.ReadMemStats(&m)
        
        fmt.Printf("=== Memory Stats ===\n")
        fmt.Printf("Alloc = %d KB", bToKb(m.Alloc))
        fmt.Printf("TotalAlloc = %d KB", bToKb(m.TotalAlloc))
        fmt.Printf("Sys = %d KB", bToKb(m.Sys))
        fmt.Printf("NumGC = %d\n", m.NumGC)
        fmt.Printf("HeapAlloc = %d KB", bToKb(m.HeapAlloc))
        fmt.Printf("HeapSys = %d KB", bToKb(m.HeapSys))
        fmt.Printf("HeapIdle = %d KB", bToKb(m.HeapIdle))
        fmt.Printf("HeapInuse = %d KB", bToKb(m.HeapInuse))
        fmt.Printf("StackInuse = %d KB", bToKb(m.StackInuse))
        fmt.Printf("StackSys = %d KB\n", bToKb(m.StackSys))
    }
}

func bToKb(b uint64) uint64 {
    return b / 1024
}
```

### 3.2 大页内存优化

```go
// 使用madvise优化内存访问模式
func optimizeMemoryAccess(data []byte) {
    // 建议内核这块内存将被顺序访问
    err := syscall.Madvise(data, syscall.MADV_SEQUENTIAL)
    if err != nil {
        fmt.Printf("MADV_SEQUENTIAL failed: %v\n", err)
    }
    
    // 预读内存页
    err = syscall.Madvise(data, syscall.MADV_WILLNEED)
    if err != nil {
        fmt.Printf("MADV_WILLNEED failed: %v\n", err)
    }
}

// 内存预分配和预热
func preallocateAndWarmup(size int) []byte {
    // 分配内存
    data := make([]byte, size)
    
    // 内存预热：触发实际的物理内存分配
    pageSize := os.Getpagesize()
    for i := 0; i < len(data); i += pageSize {
        data[i] = 0
    }
    
    return data
}
```

## 4. 性能监控与调优

### 4.1 系统性能监控

```go
// CPU使用率监控
func monitorCPUUsage() {
    prevIdle, prevTotal := getCPUTimes()
    
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        idle, total := getCPUTimes()
        
        idleDelta := idle - prevIdle
        totalDelta := total - prevTotal
        
        cpuUsage := 100.0 * (1.0 - float64(idleDelta)/float64(totalDelta))
        fmt.Printf("CPU Usage: %.2f%%\n", cpuUsage)
        
        prevIdle, prevTotal = idle, total
    }
}

func getCPUTimes() (idle, total uint64) {
    data, err := os.ReadFile("/proc/stat")
    if err != nil {
        return 0, 0
    }
    
    lines := strings.Split(string(data), "\n")
    for _, line := range lines {
        if strings.HasPrefix(line, "cpu ") {
            fields := strings.Fields(line)
            if len(fields) >= 8 {
                user, _ := strconv.ParseUint(fields[1], 10, 64)
                nice, _ := strconv.ParseUint(fields[2], 10, 64)
                system, _ := strconv.ParseUint(fields[3], 10, 64)
                idle, _ := strconv.ParseUint(fields[4], 10, 64)
                iowait, _ := strconv.ParseUint(fields[5], 10, 64)
                irq, _ := strconv.ParseUint(fields[6], 10, 64)
                softirq, _ := strconv.ParseUint(fields[7], 10, 64)
                
                total = user + nice + system + idle + iowait + irq + softirq
                return idle, total
            }
        }
    }
    return 0, 0
}

// 网络I/O监控
func monitorNetworkIO() {
    prevRx, prevTx := getNetworkStats()
    
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        rx, tx := getNetworkStats()
        
        rxRate := (rx - prevRx) / 1024 // KB/s
        txRate := (tx - prevTx) / 1024 // KB/s
        
        fmt.Printf("Network I/O - RX: %d KB/s, TX: %d KB/s\n", rxRate, txRate)
        
        prevRx, prevTx = rx, tx
    }
}

func getNetworkStats() (rx, tx uint64) {
    data, err := os.ReadFile("/proc/net/dev")
    if err != nil {
        return 0, 0
    }
    
    lines := strings.Split(string(data), "\n")
    for _, line := range lines {
        if strings.Contains(line, "eth0") || strings.Contains(line, "ens") {
            fields := strings.Fields(line)
            if len(fields) >= 10 {
                rxBytes, _ := strconv.ParseUint(fields[1], 10, 64)
                txBytes, _ := strconv.ParseUint(fields[9], 10, 64)
                rx += rxBytes
                tx += txBytes
            }
        }
    }
    return rx, tx
}
```

### 4.2 性能调优技巧

```go
// CPU亲和性设置
func setCPUAffinity(cpus []int) error {
    var cpuSet syscall.CPUSet
    cpuSet.Zero()
    
    for _, cpu := range cpus {
        cpuSet.Set(cpu)
    }
    
    return syscall.SchedSetaffinity(0, &cpuSet)
}

// 进程优先级调整
func setProcessPriority(priority int) error {
    return syscall.Setpriority(syscall.PRIO_PROCESS, 0, priority)
}

// 实时调度策略设置
func setRealtimeScheduling() error {
    param := &syscall.SchedParam{Priority: 50}
    return syscall.SchedSetscheduler(0, syscall.SCHED_FIFO, param)
}

// 内存锁定（防止swap）
func lockMemory(data []byte) error {
    return syscall.Mlock(data)
}

func unlockMemory(data []byte) error {
    return syscall.Munlock(data)
}

// 文件系统优化
func optimizeFileAccess(file *os.File) error {
    fd := int(file.Fd())
    
    // 设置文件访问模式
    err := syscall.Fadvise(fd, 0, 0, syscall.FADV_SEQUENTIAL)
    if err != nil {
        return err
    }
    
    // 预读文件内容
    return syscall.Fadvise(fd, 0, 0, syscall.FADV_WILLNEED)
}
```

## 5. 故障排查与调试

### 5.1 系统调用跟踪

```bash
# 使用strace跟踪系统调用
strace -p <pid>                    # 跟踪指定进程
strace -c ./program               # 统计系统调用
strace -e trace=file ./program    # 只跟踪文件相关调用
strace -e trace=network ./program # 只跟踪网络相关调用

# 使用perf进行性能分析
perf record ./program             # 记录性能数据
perf report                       # 分析性能报告
perf top                         # 实时性能监控
perf stat ./program              # 统计性能指标
```

### 5.2 内存泄漏检测

```go
// 内存泄漏检测工具
func detectMemoryLeaks() {
    // 启用内存分析
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 定期检查内存使用
    ticker := time.NewTicker(time.Minute)
    defer ticker.Stop()
    
    var lastAlloc uint64
    
    for range ticker.C {
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        
        if m.Alloc > lastAlloc*2 && lastAlloc > 0 {
            fmt.Printf("WARNING: Memory usage doubled! Current: %d KB, Previous: %d KB\n",
                m.Alloc/1024, lastAlloc/1024)
            
            // 强制GC
            runtime.GC()
            runtime.ReadMemStats(&m)
            fmt.Printf("After GC: %d KB\n", m.Alloc/1024)
        }
        
        lastAlloc = m.Alloc
    }
}

// Goroutine泄漏检测
func detectGoroutineLeaks() {
    ticker := time.NewTicker(time.Minute)
    defer ticker.Stop()
    
    lastCount := runtime.NumGoroutine()
    
    for range ticker.C {
        currentCount := runtime.NumGoroutine()
        
        if currentCount > lastCount*2 && lastCount > 10 {
            fmt.Printf("WARNING: Goroutine count doubled! Current: %d, Previous: %d\n",
                currentCount, lastCount)
            
            // 打印goroutine堆栈
            buf := make([]byte, 1<<20)
            stackSize := runtime.Stack(buf, true)
            fmt.Printf("Goroutine stacks:\n%s\n", buf[:stackSize])
        }
        
        lastCount = currentCount
    }
}
```

## 6. 面试重点问题

### Q1: 解释epoll的工作原理和优势？
- **工作原理**：基于事件通知，内核维护就绪文件描述符列表
- **优势**：O(1)复杂度，支持大量并发连接，边缘触发模式
- **应用场景**：高并发网络服务器，如Nginx、Redis

### Q2: 什么是零拷贝技术？
- **定义**：数据在内核空间和用户空间之间传输时避免不必要的拷贝
- **实现方式**：sendfile、splice、mmap
- **优势**：减少CPU使用，提高I/O性能，降低内存带宽消耗

### Q3: 如何进行Linux系统性能调优？
1. **CPU调优**：设置CPU亲和性，调整调度策略
2. **内存调优**：使用大页内存，优化内存分配
3. **I/O调优**：使用异步I/O，优化文件系统参数
4. **网络调优**：调整TCP参数，使用高性能网络模型

### Q4: 常用的Linux性能监控工具？
- **系统级**：top、htop、iotop、iftop、vmstat、iostat
- **进程级**：ps、pstree、lsof、netstat、ss
- **性能分析**：perf、strace、tcpdump、wireshark
- **内存分析**：valgrind、pprof、/proc/meminfo

这些Linux系统编程和性能调优技术是构建高性能系统的基础，掌握它们对于系统优化至关重要。
