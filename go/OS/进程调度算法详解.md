# 进程调度算法详解

## 进程调度基础

### 1. 调度的基本概念

#### 调度层次
- **高级调度（作业调度）**：决定哪些作业进入系统
- **中级调度（内存调度）**：决定哪些进程换入换出内存
- **低级调度（进程调度）**：决定哪个就绪进程获得CPU

#### 调度时机
- 进程从运行状态转为等待状态
- 进程从运行状态转为就绪状态
- 进程从等待状态转为就绪状态
- 进程终止

### 2. 调度算法分类

#### 抢占式 vs 非抢占式
- **抢占式**：可以强制收回正在运行进程的CPU
- **非抢占式**：进程主动放弃CPU才能调度其他进程

#### 调度目标
- **批处理系统**：吞吐量、周转时间
- **交互式系统**：响应时间、公平性
- **实时系统**：满足截止时间、可预测性

## 经典调度算法

### 1. 先来先服务（FCFS）

#### 算法原理
按照进程到达的先后顺序进行调度，非抢占式。

#### 实现代码
```c
typedef struct Process {
    int pid;
    int arrival_time;
    int burst_time;
    int waiting_time;
    int turnaround_time;
    struct Process* next;
} Process;

void fcfs_schedule(Process* processes[], int n) {
    // 按到达时间排序
    sort_by_arrival_time(processes, n);
    
    int current_time = 0;
    
    for (int i = 0; i < n; i++) {
        Process* p = processes[i];
        
        // 如果CPU空闲，等待进程到达
        if (current_time < p->arrival_time) {
            current_time = p->arrival_time;
        }
        
        // 计算等待时间和周转时间
        p->waiting_time = current_time - p->arrival_time;
        p->turnaround_time = p->waiting_time + p->burst_time;
        
        // 更新当前时间
        current_time += p->burst_time;
        
        printf("进程 %d: 等待时间=%d, 周转时间=%d\n", 
               p->pid, p->waiting_time, p->turnaround_time);
    }
}
```

#### 优缺点
- **优点**：简单公平，无饥饿问题
- **缺点**：平均等待时间长，不适合交互式系统

### 2. 最短作业优先（SJF）

#### 非抢占式SJF
```c
void sjf_non_preemptive(Process* processes[], int n) {
    int completed = 0;
    int current_time = 0;
    bool is_completed[n];
    memset(is_completed, false, sizeof(is_completed));
    
    while (completed < n) {
        int shortest_job = -1;
        int min_burst_time = INT_MAX;
        
        // 找到已到达且未完成的最短作业
        for (int i = 0; i < n; i++) {
            if (!is_completed[i] && 
                processes[i]->arrival_time <= current_time &&
                processes[i]->burst_time < min_burst_time) {
                min_burst_time = processes[i]->burst_time;
                shortest_job = i;
            }
        }
        
        if (shortest_job == -1) {
            // 没有就绪进程，时间前进
            current_time++;
            continue;
        }
        
        Process* p = processes[shortest_job];
        p->waiting_time = current_time - p->arrival_time;
        p->turnaround_time = p->waiting_time + p->burst_time;
        current_time += p->burst_time;
        is_completed[shortest_job] = true;
        completed++;
    }
}
```

#### 抢占式SJF（SRTF）
```c
void srtf_schedule(Process* processes[], int n) {
    int completed = 0;
    int current_time = 0;
    int remaining_time[n];
    
    // 初始化剩余时间
    for (int i = 0; i < n; i++) {
        remaining_time[i] = processes[i]->burst_time;
    }
    
    while (completed < n) {
        int shortest_remaining = -1;
        int min_remaining_time = INT_MAX;
        
        // 找到剩余时间最短的就绪进程
        for (int i = 0; i < n; i++) {
            if (processes[i]->arrival_time <= current_time &&
                remaining_time[i] > 0 &&
                remaining_time[i] < min_remaining_time) {
                min_remaining_time = remaining_time[i];
                shortest_remaining = i;
            }
        }
        
        if (shortest_remaining == -1) {
            current_time++;
            continue;
        }
        
        // 执行一个时间单位
        remaining_time[shortest_remaining]--;
        current_time++;
        
        // 检查是否完成
        if (remaining_time[shortest_remaining] == 0) {
            completed++;
            Process* p = processes[shortest_remaining];
            p->turnaround_time = current_time - p->arrival_time;
            p->waiting_time = p->turnaround_time - p->burst_time;
        }
    }
}
```

### 3. 优先级调度

#### 静态优先级调度
```c
typedef struct {
    int pid;
    int priority;
    int arrival_time;
    int burst_time;
    int waiting_time;
    int turnaround_time;
} PriorityProcess;

void priority_schedule(PriorityProcess* processes[], int n) {
    int completed = 0;
    int current_time = 0;
    bool is_completed[n];
    memset(is_completed, false, sizeof(is_completed));
    
    while (completed < n) {
        int highest_priority = -1;
        int max_priority = -1;
        
        // 找到优先级最高的就绪进程
        for (int i = 0; i < n; i++) {
            if (!is_completed[i] && 
                processes[i]->arrival_time <= current_time &&
                processes[i]->priority > max_priority) {
                max_priority = processes[i]->priority;
                highest_priority = i;
            }
        }
        
        if (highest_priority == -1) {
            current_time++;
            continue;
        }
        
        PriorityProcess* p = processes[highest_priority];
        p->waiting_time = current_time - p->arrival_time;
        p->turnaround_time = p->waiting_time + p->burst_time;
        current_time += p->burst_time;
        is_completed[highest_priority] = true;
        completed++;
    }
}
```

#### 动态优先级调度（老化机制）
```c
void aging_priority_schedule(PriorityProcess* processes[], int n) {
    int current_time = 0;
    int completed = 0;
    int waiting_time[n];
    memset(waiting_time, 0, sizeof(waiting_time));
    
    while (completed < n) {
        // 更新所有等待进程的优先级（老化）
        for (int i = 0; i < n; i++) {
            if (processes[i]->arrival_time <= current_time && 
                !is_completed[i]) {
                waiting_time[i]++;
                // 每等待10个时间单位，优先级+1
                if (waiting_time[i] % 10 == 0) {
                    processes[i]->priority++;
                }
            }
        }
        
        // 选择优先级最高的进程执行
        int selected = select_highest_priority(processes, n, current_time);
        if (selected != -1) {
            execute_process(processes[selected], current_time);
            completed++;
        }
        
        current_time++;
    }
}
```

### 4. 轮转调度（RR）

#### 基本轮转调度
```c
typedef struct {
    int pid;
    int arrival_time;
    int burst_time;
    int remaining_time;
    int waiting_time;
    int turnaround_time;
} RRProcess;

void round_robin_schedule(RRProcess* processes[], int n, int time_quantum) {
    int current_time = 0;
    int completed = 0;
    
    // 创建就绪队列
    Queue* ready_queue = create_queue();
    
    // 将到达时间为0的进程加入队列
    for (int i = 0; i < n; i++) {
        if (processes[i]->arrival_time == 0) {
            enqueue(ready_queue, processes[i]);
        }
        processes[i]->remaining_time = processes[i]->burst_time;
    }
    
    while (completed < n) {
        if (is_empty(ready_queue)) {
            current_time++;
            // 检查是否有新进程到达
            check_new_arrivals(processes, n, ready_queue, current_time);
            continue;
        }
        
        RRProcess* current_process = dequeue(ready_queue);
        
        // 执行时间片
        int execution_time = min(time_quantum, current_process->remaining_time);
        current_process->remaining_time -= execution_time;
        current_time += execution_time;
        
        // 检查新到达的进程
        check_new_arrivals(processes, n, ready_queue, current_time);
        
        if (current_process->remaining_time == 0) {
            // 进程完成
            completed++;
            current_process->turnaround_time = current_time - current_process->arrival_time;
            current_process->waiting_time = current_process->turnaround_time - current_process->burst_time;
        } else {
            // 进程未完成，重新加入队列
            enqueue(ready_queue, current_process);
        }
    }
}
```

#### 多级反馈队列
```c
#define MAX_LEVELS 3

typedef struct {
    Queue* queues[MAX_LEVELS];
    int time_quantum[MAX_LEVELS];
} MultilevelFeedbackQueue;

void multilevel_feedback_schedule(RRProcess* processes[], int n) {
    MultilevelFeedbackQueue* mfq = create_mfq();
    
    // 设置各级队列的时间片
    mfq->time_quantum[0] = 8;   // 最高优先级，时间片8
    mfq->time_quantum[1] = 16;  // 中等优先级，时间片16
    mfq->time_quantum[2] = INT_MAX; // 最低优先级，FCFS
    
    int current_time = 0;
    int completed = 0;
    
    // 初始时所有进程进入最高优先级队列
    for (int i = 0; i < n; i++) {
        if (processes[i]->arrival_time == 0) {
            enqueue(mfq->queues[0], processes[i]);
        }
    }
    
    while (completed < n) {
        RRProcess* current_process = NULL;
        int current_level = -1;
        
        // 从最高优先级队列开始查找
        for (int level = 0; level < MAX_LEVELS; level++) {
            if (!is_empty(mfq->queues[level])) {
                current_process = dequeue(mfq->queues[level]);
                current_level = level;
                break;
            }
        }
        
        if (current_process == NULL) {
            current_time++;
            check_new_arrivals(processes, n, mfq->queues[0], current_time);
            continue;
        }
        
        // 执行进程
        int quantum = mfq->time_quantum[current_level];
        int execution_time = min(quantum, current_process->remaining_time);
        current_process->remaining_time -= execution_time;
        current_time += execution_time;
        
        check_new_arrivals(processes, n, mfq->queues[0], current_time);
        
        if (current_process->remaining_time == 0) {
            // 进程完成
            completed++;
            calculate_times(current_process, current_time);
        } else {
            // 进程未完成，降级到下一级队列
            int next_level = min(current_level + 1, MAX_LEVELS - 1);
            enqueue(mfq->queues[next_level], current_process);
        }
    }
}
```

## 实时调度算法

### 1. 速率单调调度（RMS）

#### 算法实现
```c
typedef struct {
    int task_id;
    int period;         // 周期
    int execution_time; // 执行时间
    int deadline;       // 截止时间
    int next_release;   // 下次释放时间
    int remaining_time; // 剩余执行时间
} RTTask;

bool rms_schedule(RTTask* tasks[], int n, int simulation_time) {
    // 按周期排序（周期越短优先级越高）
    sort_by_period(tasks, n);
    
    // 可调度性分析
    double utilization = 0;
    for (int i = 0; i < n; i++) {
        utilization += (double)tasks[i]->execution_time / tasks[i]->period;
    }
    
    double bound = n * (pow(2, 1.0/n) - 1);
    if (utilization > bound) {
        printf("任务集不可调度，利用率=%.2f, 界限=%.2f\n", utilization, bound);
        return false;
    }
    
    // 模拟调度
    for (int time = 0; time < simulation_time; time++) {
        // 检查任务释放
        for (int i = 0; i < n; i++) {
            if (time == tasks[i]->next_release) {
                tasks[i]->remaining_time = tasks[i]->execution_time;
                tasks[i]->deadline = time + tasks[i]->period;
                tasks[i]->next_release += tasks[i]->period;
            }
        }
        
        // 选择优先级最高的就绪任务
        int selected = -1;
        int highest_priority = INT_MAX;
        
        for (int i = 0; i < n; i++) {
            if (tasks[i]->remaining_time > 0 && tasks[i]->period < highest_priority) {
                highest_priority = tasks[i]->period;
                selected = i;
            }
        }
        
        if (selected != -1) {
            tasks[selected]->remaining_time--;
            printf("时间 %d: 执行任务 %d\n", time, tasks[selected]->task_id);
            
            // 检查截止时间
            if (time >= tasks[selected]->deadline && tasks[selected]->remaining_time > 0) {
                printf("任务 %d 错过截止时间！\n", tasks[selected]->task_id);
                return false;
            }
        }
    }
    
    return true;
}
```

### 2. 最早截止时间优先（EDF）

#### 算法实现
```c
bool edf_schedule(RTTask* tasks[], int n, int simulation_time) {
    for (int time = 0; time < simulation_time; time++) {
        // 检查任务释放
        for (int i = 0; i < n; i++) {
            if (time == tasks[i]->next_release) {
                tasks[i]->remaining_time = tasks[i]->execution_time;
                tasks[i]->deadline = time + tasks[i]->period;
                tasks[i]->next_release += tasks[i]->period;
            }
        }
        
        // 选择截止时间最早的就绪任务
        int selected = -1;
        int earliest_deadline = INT_MAX;
        
        for (int i = 0; i < n; i++) {
            if (tasks[i]->remaining_time > 0 && tasks[i]->deadline < earliest_deadline) {
                earliest_deadline = tasks[i]->deadline;
                selected = i;
            }
        }
        
        if (selected != -1) {
            tasks[selected]->remaining_time--;
            printf("时间 %d: 执行任务 %d (截止时间: %d)\n", 
                   time, tasks[selected]->task_id, tasks[selected]->deadline);
            
            if (time >= tasks[selected]->deadline && tasks[selected]->remaining_time > 0) {
                printf("任务 %d 错过截止时间！\n", tasks[selected]->task_id);
                return false;
            }
        }
    }
    
    return true;
}
```

## 调度算法性能分析

### 1. 性能指标

#### 计算平均指标
```c
void calculate_average_metrics(Process* processes[], int n) {
    double total_waiting_time = 0;
    double total_turnaround_time = 0;
    double total_response_time = 0;
    
    for (int i = 0; i < n; i++) {
        total_waiting_time += processes[i]->waiting_time;
        total_turnaround_time += processes[i]->turnaround_time;
        total_response_time += processes[i]->response_time;
    }
    
    printf("平均等待时间: %.2f\n", total_waiting_time / n);
    printf("平均周转时间: %.2f\n", total_turnaround_time / n);
    printf("平均响应时间: %.2f\n", total_response_time / n);
    
    // 计算CPU利用率
    int total_burst_time = 0;
    int total_time = 0;
    for (int i = 0; i < n; i++) {
        total_burst_time += processes[i]->burst_time;
        if (processes[i]->turnaround_time + processes[i]->arrival_time > total_time) {
            total_time = processes[i]->turnaround_time + processes[i]->arrival_time;
        }
    }
    
    printf("CPU利用率: %.2f%%\n", (double)total_burst_time / total_time * 100);
}
```

### 2. 算法比较

| 算法 | 平均等待时间 | 响应时间 | 公平性 | 饥饿问题 | 适用场景 |
|------|--------------|----------|--------|----------|----------|
| FCFS | 长 | 差 | 好 | 无 | 批处理 |
| SJF | 最优 | 差 | 差 | 有 | 批处理 |
| 优先级 | 中等 | 好 | 差 | 有 | 实时系统 |
| RR | 中等 | 好 | 好 | 无 | 交互式 |
| 多级反馈 | 中等 | 很好 | 好 | 无 | 通用 |

## 现代调度算法

### 1. 完全公平调度器（CFS）

#### 虚拟运行时间概念
```c
typedef struct {
    int pid;
    long long vruntime;     // 虚拟运行时间
    int nice_value;         // nice值
    int weight;             // 权重
    struct rb_node rb_node; // 红黑树节点
} CFSTask;

// 计算权重
int nice_to_weight[40] = {
    88761, 71755, 56483, 46273, 36291,
    29154, 23254, 18705, 14949, 11916,
    9548, 7620, 6100, 4904, 3906,
    3121, 2501, 1991, 1586, 1277,
    1024, 820, 655, 526, 423,
    335, 272, 215, 172, 137,
    110, 87, 70, 56, 45,
    36, 29, 23, 18, 15
};

void update_vruntime(CFSTask* task, long long runtime) {
    // vruntime += runtime * NICE_0_LOAD / weight
    task->vruntime += runtime * 1024 / task->weight;
}

CFSTask* pick_next_task(struct rb_root* cfs_rq) {
    struct rb_node* leftmost = rb_first(cfs_rq);
    if (!leftmost) {
        return NULL;
    }
    
    return rb_entry(leftmost, CFSTask, rb_node);
}
```

### 2. 多核调度

#### 负载均衡
```c
typedef struct {
    int cpu_id;
    int load;
    Queue* run_queue;
    CFSTask* current_task;
} CPU;

void load_balance(CPU* cpus[], int num_cpus) {
    // 计算平均负载
    int total_load = 0;
    for (int i = 0; i < num_cpus; i++) {
        total_load += cpus[i]->load;
    }
    int avg_load = total_load / num_cpus;
    
    // 找到最忙和最闲的CPU
    int busiest_cpu = 0, idlest_cpu = 0;
    for (int i = 1; i < num_cpus; i++) {
        if (cpus[i]->load > cpus[busiest_cpu]->load) {
            busiest_cpu = i;
        }
        if (cpus[i]->load < cpus[idlest_cpu]->load) {
            idlest_cpu = i;
        }
    }
    
    // 如果负载差异超过阈值，进行迁移
    if (cpus[busiest_cpu]->load - cpus[idlest_cpu]->load > avg_load / 4) {
        migrate_task(cpus[busiest_cpu], cpus[idlest_cpu]);
    }
}
```

## 面试要点总结

1. **基本概念**：理解调度的目标和分类
2. **经典算法**：掌握FCFS、SJF、优先级、RR等算法的原理和实现
3. **实时调度**：了解RMS和EDF算法的特点
4. **性能分析**：能够计算和比较不同算法的性能指标
5. **现代调度**：了解CFS和多核调度的基本原理
6. **实际应用**：理解不同调度算法的适用场景
