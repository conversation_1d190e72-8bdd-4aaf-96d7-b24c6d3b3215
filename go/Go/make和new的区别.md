# make和new的区别

## 基本概念

### new函数
- **作用**：分配内存并返回指向该内存的指针
- **返回值**：指针类型
- **初始化**：零值初始化
- **适用类型**：所有类型

### make函数
- **作用**：创建并初始化slice、map、channel
- **返回值**：类型本身（不是指针）
- **初始化**：完全初始化，可以直接使用
- **适用类型**：仅限slice、map、channel

## 详细对比

### 语法差异
```go
// new的使用
var p *int = new(int)        // 返回*int，值为0
var s *[]int = new([]int)    // 返回*[]int，值为nil slice的指针

// make的使用
var slice []int = make([]int, 5)      // 返回[]int，长度为5的slice
var m map[string]int = make(map[string]int)  // 返回map[string]int
var ch chan int = make(chan int)      // 返回chan int
```

### 内存分配差异
```go
// new：只分配内存，不初始化
p := new([]int)
fmt.Println(p)        // &[]
fmt.Println(*p)       // []
fmt.Println(*p == nil) // true，不能直接使用

// make：分配内存并初始化
s := make([]int, 5)
fmt.Println(s)        // [0 0 0 0 0]
fmt.Println(len(s))   // 5，可以直接使用
```

## 使用场景

### new的使用场景
```go
// 1. 需要指针类型
type Person struct {
    Name string
    Age  int
}

p := new(Person)  // *Person
p.Name = "Alice"
p.Age = 30

// 2. 基本类型的指针
num := new(int)
*num = 42

// 3. 结构体指针
var user *Person = new(Person)
```

### make的使用场景
```go
// 1. 创建slice
slice := make([]int, 5, 10)  // 长度5，容量10

// 2. 创建map
m := make(map[string]int)
m["key"] = 100

// 3. 创建channel
ch := make(chan int, 5)  // 带缓冲的channel
```

## 常见错误

### 错误使用new创建slice/map/channel
```go
// 错误：不能直接使用
var s *[]int = new([]int)
// *s = append(*s, 1)  // panic: 对nil slice进行append

var m *map[string]int = new(map[string]int)
// (*m)["key"] = 1     // panic: 对nil map进行赋值

// 正确：需要先初始化
*s = make([]int, 0)
*s = append(*s, 1)

*m = make(map[string]int)
(*m)["key"] = 1
```

### 混淆返回类型
```go
// new返回指针
p := new([]int)      // p的类型是*[]int
fmt.Printf("%T\n", p) // *[]int

// make返回值类型
s := make([]int, 5)   // s的类型是[]int
fmt.Printf("%T\n", s) // []int
```

## 面试常见问题

### Q1: new和make的主要区别是什么？

**答案**：
1. **返回类型**：new返回指针，make返回值类型
2. **适用类型**：new适用所有类型，make仅适用slice、map、channel
3. **初始化**：new只分配零值内存，make完全初始化可直接使用
4. **使用场景**：new用于需要指针的场景，make用于创建引用类型

### Q2: 什么时候使用new，什么时候使用make？

**答案**：
- **使用new**：需要指针类型，或者需要零值初始化的场景
- **使用make**：创建slice、map、channel时必须使用make
- **实际开发**：更多使用字面量语法和make，new使用较少

### Q3: 下面代码的输出是什么？
```go
var p *[]int = new([]int)
var s []int = make([]int, 0)
fmt.Println(p == nil)   // ?
fmt.Println(*p == nil)  // ?
fmt.Println(s == nil)   // ?
```

**答案**：
```go
fmt.Println(p == nil)   // false，p是指向slice的指针
fmt.Println(*p == nil)  // true，*p是nil slice
fmt.Println(s == nil)   // false，s是空slice但不是nil
```

## 最佳实践

1. **优先使用字面量**：`var s []int` 而不是 `s := new([]int)`
2. **make用于引用类型**：slice、map、channel必须用make
3. **new用于指针需求**：确实需要指针时才使用new
4. **理解零值**：了解不同类型的零值含义
5. **避免混淆**：记住返回类型的差异

## 总结

- **new**：通用的内存分配函数，返回指针，零值初始化
- **make**：专用于slice、map、channel的创建函数，返回可用的值
- **选择原则**：根据需要的类型和使用场景选择合适的函数
