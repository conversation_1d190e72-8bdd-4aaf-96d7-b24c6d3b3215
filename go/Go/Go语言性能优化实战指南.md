# Go语言性能优化实战指南

## 性能优化基础

### 1. 性能分析工具

#### pprof性能分析
```go
package main

import (
    "net/http"
    _ "net/http/pprof"
    "runtime"
    "time"
)

func main() {
    // 启动pprof服务
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 模拟CPU密集型任务
    go cpuIntensiveTask()
    
    // 模拟内存分配
    go memoryIntensiveTask()
    
    select {} // 保持程序运行
}

func cpuIntensiveTask() {
    for {
        for i := 0; i < 1000000; i++ {
            _ = i * i
        }
        time.Sleep(100 * time.Millisecond)
    }
}

func memoryIntensiveTask() {
    var data [][]byte
    for {
        // 分配大量内存
        chunk := make([]byte, 1024*1024) // 1MB
        data = append(data, chunk)
        
        // 定期清理，避免OOM
        if len(data) > 100 {
            data = data[:0]
            runtime.GC()
        }
        
        time.Sleep(50 * time.Millisecond)
    }
}
```

#### 基准测试
```go
package main

import (
    "testing"
    "strings"
    "fmt"
)

// 字符串拼接性能对比
func BenchmarkStringConcat(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var result string
        for j := 0; j < 100; j++ {
            result += "hello"
        }
    }
}

func BenchmarkStringBuilder(b *testing.B) {
    for i := 0; i < b.N; i++ {
        var builder strings.Builder
        for j := 0; j < 100; j++ {
            builder.WriteString("hello")
        }
        _ = builder.String()
    }
}

func BenchmarkStringJoin(b *testing.B) {
    for i := 0; i < b.N; i++ {
        parts := make([]string, 100)
        for j := 0; j < 100; j++ {
            parts[j] = "hello"
        }
        _ = strings.Join(parts, "")
    }
}

// 运行基准测试
// go test -bench=. -benchmem
```

### 2. 内存优化

#### 对象池模式
```go
package main

import (
    "sync"
    "fmt"
)

// 对象池减少GC压力
type Buffer struct {
    data []byte
}

func (b *Buffer) Reset() {
    b.data = b.data[:0]
}

var bufferPool = sync.Pool{
    New: func() interface{} {
        return &Buffer{
            data: make([]byte, 0, 1024),
        }
    },
}

func processData(input []byte) []byte {
    // 从池中获取buffer
    buf := bufferPool.Get().(*Buffer)
    defer func() {
        buf.Reset()
        bufferPool.Put(buf) // 归还到池中
    }()
    
    // 处理数据
    buf.data = append(buf.data, input...)
    buf.data = append(buf.data, []byte(" processed")...)
    
    // 返回副本
    result := make([]byte, len(buf.data))
    copy(result, buf.data)
    return result
}

// 性能对比测试
func BenchmarkWithoutPool(b *testing.B) {
    input := []byte("test data")
    for i := 0; i < b.N; i++ {
        buf := &Buffer{data: make([]byte, 0, 1024)}
        buf.data = append(buf.data, input...)
        buf.data = append(buf.data, []byte(" processed")...)
    }
}

func BenchmarkWithPool(b *testing.B) {
    input := []byte("test data")
    for i := 0; i < b.N; i++ {
        _ = processData(input)
    }
}
```

#### 内存预分配
```go
package main

import (
    "testing"
)

// 切片预分配优化
func processSliceWithoutPrealloc(n int) []int {
    var result []int
    for i := 0; i < n; i++ {
        result = append(result, i*i)
    }
    return result
}

func processSliceWithPrealloc(n int) []int {
    result := make([]int, 0, n) // 预分配容量
    for i := 0; i < n; i++ {
        result = append(result, i*i)
    }
    return result
}

func processSliceWithIndex(n int) []int {
    result := make([]int, n) // 直接分配长度
    for i := 0; i < n; i++ {
        result[i] = i * i
    }
    return result
}

func BenchmarkSliceWithoutPrealloc(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = processSliceWithoutPrealloc(1000)
    }
}

func BenchmarkSliceWithPrealloc(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = processSliceWithPrealloc(1000)
    }
}

func BenchmarkSliceWithIndex(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = processSliceWithIndex(1000)
    }
}
```

### 3. CPU优化

#### 避免不必要的内存分配
```go
package main

import (
    "testing"
    "strconv"
)

// 字符串转换优化
func convertToStringBad(nums []int) []string {
    var result []string
    for _, num := range nums {
        result = append(result, strconv.Itoa(num))
    }
    return result
}

func convertToStringGood(nums []int) []string {
    result := make([]string, len(nums)) // 预分配
    for i, num := range nums {
        result[i] = strconv.Itoa(num)
    }
    return result
}

func convertToStringBest(nums []int) []string {
    result := make([]string, 0, len(nums)) // 预分配容量
    for _, num := range nums {
        result = append(result, strconv.Itoa(num))
    }
    return result
}

// 缓存优化
type Calculator struct {
    cache map[int]int
}

func NewCalculator() *Calculator {
    return &Calculator{
        cache: make(map[int]int),
    }
}

func (c *Calculator) ExpensiveCalculation(n int) int {
    if result, exists := c.cache[n]; exists {
        return result
    }
    
    // 模拟昂贵的计算
    result := 0
    for i := 0; i < n; i++ {
        result += i * i
    }
    
    c.cache[n] = result
    return result
}
```

#### 并发优化
```go
package main

import (
    "runtime"
    "sync"
    "testing"
)

// 并发处理大量数据
func processDataSequential(data []int) []int {
    result := make([]int, len(data))
    for i, v := range data {
        result[i] = expensiveOperation(v)
    }
    return result
}

func processDataConcurrent(data []int) []int {
    numWorkers := runtime.NumCPU()
    chunkSize := len(data) / numWorkers
    if chunkSize == 0 {
        chunkSize = 1
    }
    
    result := make([]int, len(data))
    var wg sync.WaitGroup
    
    for i := 0; i < len(data); i += chunkSize {
        end := i + chunkSize
        if end > len(data) {
            end = len(data)
        }
        
        wg.Add(1)
        go func(start, end int) {
            defer wg.Done()
            for j := start; j < end; j++ {
                result[j] = expensiveOperation(data[j])
            }
        }(i, end)
    }
    
    wg.Wait()
    return result
}

func expensiveOperation(n int) int {
    // 模拟CPU密集型操作
    sum := 0
    for i := 0; i < n%1000; i++ {
        sum += i
    }
    return sum
}

// 工作池模式
func processDataWithWorkerPool(data []int) []int {
    numWorkers := runtime.NumCPU()
    jobs := make(chan job, len(data))
    results := make(chan result, len(data))
    
    // 启动工作者
    var wg sync.WaitGroup
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go worker(jobs, results, &wg)
    }
    
    // 发送任务
    go func() {
        for i, v := range data {
            jobs <- job{index: i, value: v}
        }
        close(jobs)
    }()
    
    // 等待工作者完成
    go func() {
        wg.Wait()
        close(results)
    }()
    
    // 收集结果
    output := make([]int, len(data))
    for r := range results {
        output[r.index] = r.value
    }
    
    return output
}

type job struct {
    index int
    value int
}

type result struct {
    index int
    value int
}

func worker(jobs <-chan job, results chan<- result, wg *sync.WaitGroup) {
    defer wg.Done()
    for j := range jobs {
        results <- result{
            index: j.index,
            value: expensiveOperation(j.value),
        }
    }
}
```

### 4. I/O优化

#### 缓冲I/O
```go
package main

import (
    "bufio"
    "io"
    "os"
    "testing"
)

// 文件读取优化
func readFileUnbuffered(filename string) ([]byte, error) {
    file, err := os.Open(filename)
    if err != nil {
        return nil, err
    }
    defer file.Close()
    
    return io.ReadAll(file)
}

func readFileBuffered(filename string) ([]byte, error) {
    file, err := os.Open(filename)
    if err != nil {
        return nil, err
    }
    defer file.Close()
    
    reader := bufio.NewReader(file)
    return io.ReadAll(reader)
}

func readFileWithBuffer(filename string, bufSize int) ([]byte, error) {
    file, err := os.Open(filename)
    if err != nil {
        return nil, err
    }
    defer file.Close()
    
    reader := bufio.NewReaderSize(file, bufSize)
    return io.ReadAll(reader)
}

// 批量写入优化
func writeDataUnbuffered(filename string, data [][]byte) error {
    file, err := os.Create(filename)
    if err != nil {
        return err
    }
    defer file.Close()
    
    for _, chunk := range data {
        _, err := file.Write(chunk)
        if err != nil {
            return err
        }
    }
    
    return nil
}

func writeDataBuffered(filename string, data [][]byte) error {
    file, err := os.Create(filename)
    if err != nil {
        return err
    }
    defer file.Close()
    
    writer := bufio.NewWriter(file)
    defer writer.Flush()
    
    for _, chunk := range data {
        _, err := writer.Write(chunk)
        if err != nil {
            return err
        }
    }
    
    return nil
}
```

### 5. 网络优化

#### 连接池
```go
package main

import (
    "net/http"
    "time"
)

// HTTP客户端优化
func createOptimizedHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,              // 最大空闲连接数
        MaxIdleConnsPerHost: 10,               // 每个host的最大空闲连接数
        IdleConnTimeout:     90 * time.Second, // 空闲连接超时
        DisableCompression:  false,            // 启用压缩
        DisableKeepAlives:   false,            // 启用keep-alive
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}

// 批量请求优化
func makeRequestsConcurrent(urls []string, client *http.Client) []string {
    results := make([]string, len(urls))
    var wg sync.WaitGroup
    
    // 限制并发数
    semaphore := make(chan struct{}, 10)
    
    for i, url := range urls {
        wg.Add(1)
        go func(index int, url string) {
            defer wg.Done()
            
            semaphore <- struct{}{} // 获取信号量
            defer func() { <-semaphore }() // 释放信号量
            
            resp, err := client.Get(url)
            if err != nil {
                results[index] = "error: " + err.Error()
                return
            }
            defer resp.Body.Close()
            
            body, err := io.ReadAll(resp.Body)
            if err != nil {
                results[index] = "error: " + err.Error()
                return
            }
            
            results[index] = string(body)
        }(i, url)
    }
    
    wg.Wait()
    return results
}
```

## 面试常见问题

### Q1: Go程序性能瓶颈通常在哪里？

**答案**：
1. **内存分配**：频繁的小对象分配导致GC压力
2. **字符串操作**：大量字符串拼接操作
3. **反射使用**：过度使用反射影响性能
4. **锁竞争**：不合理的锁使用导致竞争
5. **I/O阻塞**：同步I/O操作阻塞goroutine

### Q2: 如何减少GC压力？

**答案**：
1. **对象池**：复用对象减少分配
2. **预分配**：提前分配足够的容量
3. **避免装箱**：减少interface{}的使用
4. **结构体优化**：合理安排字段顺序
5. **及时释放**：主动设置大对象为nil

### Q3: 什么时候使用指针，什么时候使用值？

**答案**：
- **使用指针**：大结构体、需要修改、避免拷贝
- **使用值**：小结构体、不可变数据、简单类型
- **性能考虑**：指针可能增加GC压力，值拷贝可能影响性能

### Q4: 如何优化字符串操作？

**答案**：
1. **strings.Builder**：高效的字符串构建
2. **预分配容量**：避免多次扩容
3. **避免+操作**：大量拼接时使用Builder
4. **字节切片**：直接操作[]byte
5. **字符串池**：复用常用字符串

## 性能优化最佳实践

1. **先测量再优化**：使用pprof等工具定位瓶颈
2. **避免过早优化**：在正确性基础上优化
3. **基准测试**：量化优化效果
4. **内存友好**：减少分配，复用对象
5. **并发合理**：避免过度并发和锁竞争
6. **I/O优化**：使用缓冲，批量操作
7. **算法优先**：选择合适的算法和数据结构
