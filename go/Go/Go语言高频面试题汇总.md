# Go语言高频面试题汇总

## 基础语法

### 1. make和new的区别
**make**：
- 只能用于slice、map、channel
- 返回类型本身，不是指针
- 会初始化内存

**new**：
- 可用于任何类型
- 返回指向零值的指针
- 只分配内存，不初始化

```go
// make示例
s := make([]int, 5)    // 创建长度为5的slice
m := make(map[string]int) // 创建map
c := make(chan int)    // 创建channel

// new示例
p := new(int)          // 返回*int，值为0
var i int
p = &i                 // 等价写法
```

### 2. slice和array的区别
**Array（数组）**：
- 固定长度，长度是类型的一部分
- 值类型，赋值时会复制整个数组
- 长度在编译时确定

**Slice（切片）**：
- 动态长度，可以扩容
- 引用类型，底层指向数组
- 包含指针、长度、容量三个字段

```go
// Array
var arr [5]int = [5]int{1, 2, 3, 4, 5}

// Slice
var slice []int = []int{1, 2, 3, 4, 5}
slice = append(slice, 6) // 可以扩容
```

### 3. interface{}和any的区别
- `interface{}`：空接口，可以存储任何类型
- `any`：Go 1.18引入的类型别名，等价于`interface{}`
- 推荐使用`any`，更简洁

## 并发编程

### 4. goroutine和thread的区别
| 特性 | Goroutine | Thread |
|------|-----------|--------|
| 创建成本 | 低(2KB栈) | 高(8MB栈) |
| 调度方式 | 协作式+抢占式 | 抢占式 |
| 调度器 | Go调度器(GMP) | OS调度器 |
| 上下文切换 | 快 | 慢 |

### 5. channel的使用场景
- **数据传递**：goroutine间通信
- **信号通知**：done channel
- **限流控制**：带缓冲channel
- **超时控制**：select + time.After

```go
// 限流示例
limiter := make(chan struct{}, 10) // 最多10个并发
for i := 0; i < 100; i++ {
    limiter <- struct{}{} // 获取令牌
    go func() {
        defer func() { <-limiter }() // 释放令牌
        // 执行任务
    }()
}
```

### 6. select的使用
```go
select {
case msg := <-ch1:
    // 处理ch1的消息
case ch2 <- value:
    // 向ch2发送数据
case <-time.After(1 * time.Second):
    // 超时处理
default:
    // 非阻塞操作
}
```

## 内存管理

### 7. GC机制
- **算法**：三色标记并发清除
- **触发时机**：堆内存增长达到GOGC阈值
- **优化**：减少对象分配，使用对象池

### 8. 内存泄漏常见场景
1. **goroutine泄漏**：goroutine无法退出
2. **channel泄漏**：channel没有正确关闭
3. **定时器泄漏**：time.Ticker没有Stop
4. **闭包引用**：闭包持有大对象引用

## 类型系统

### 9. interface的底层实现
```go
// 空接口
type eface struct {
    _type *_type      // 类型信息
    data  unsafe.Pointer // 数据指针
}

// 非空接口
type iface struct {
    tab  *itab        // 接口表
    data unsafe.Pointer // 数据指针
}
```

### 10. 类型断言和类型转换
```go
// 类型断言
var i interface{} = "hello"
s, ok := i.(string) // 安全断言
s := i.(string)     // 不安全，可能panic

// 类型转换
var i int = 42
var f float64 = float64(i) // 显式转换
```

## 错误处理

### 11. error接口
```go
type error interface {
    Error() string
}

// 自定义错误
type MyError struct {
    Code int
    Msg  string
}

func (e MyError) Error() string {
    return fmt.Sprintf("code: %d, msg: %s", e.Code, e.Msg)
}
```

### 12. panic和recover
```go
func safeDivide(a, b int) (result int, err error) {
    defer func() {
        if r := recover(); r != nil {
            err = fmt.Errorf("panic: %v", r)
        }
    }()
    
    if b == 0 {
        panic("division by zero")
    }
    return a / b, nil
}
```

## 性能优化

### 13. 字符串拼接性能
```go
// 低效：频繁分配内存
var s string
for i := 0; i < 1000; i++ {
    s += "a"
}

// 高效：使用strings.Builder
var builder strings.Builder
for i := 0; i < 1000; i++ {
    builder.WriteString("a")
}
s := builder.String()
```

### 14. map并发安全
```go
// 不安全：普通map
m := make(map[string]int)

// 安全方案1：sync.Map
var sm sync.Map
sm.Store("key", "value")

// 安全方案2：读写锁
type SafeMap struct {
    mu sync.RWMutex
    m  map[string]int
}
```

## 面试要点总结
1. **基础概念**：make/new、slice/array、interface等
2. **并发模型**：goroutine、channel、select的使用
3. **内存管理**：GC机制、内存泄漏预防
4. **性能优化**：字符串拼接、map并发、对象池
5. **错误处理**：error接口、panic/recover机制
