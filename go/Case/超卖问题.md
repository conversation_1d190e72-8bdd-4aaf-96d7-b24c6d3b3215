# 超卖问题解决方案

## 问题定义
高并发场景下，多个用户同时购买导致实际销售数量超过库存数量。

## 解决方案

### 1. 数据库锁机制
**悲观锁**
```sql
SELECT stock FROM products WHERE id = ? FOR UPDATE;
UPDATE products SET stock = stock - 1 WHERE id = ? AND stock > 0;
```
- 优点：数据一致性强
- 缺点：性能低，易死锁

**乐观锁**
```sql
UPDATE products
SET stock = stock - 1, version = version + 1
WHERE id = ? AND version = ? AND stock > 0;
```
- 优点：性能好
- 缺点：高并发下重试多

### 2. Redis原子操作
```go
stock := rdb.Decr(ctx, "stock:"+productID).Val()
if stock < 0 {
    rdb.Incr(ctx, "stock:"+productID) // 回滚
    return errors.New("库存不足")
}
```

### 3. 分布式锁
```go
lockKey := "lock:product:" + productID
if rdb.SetNX(ctx, lockKey, "1", 10*time.Second).Val() {
    defer rdb.Del(ctx, lockKey)
    // 执行库存扣减
}
```

### 4. 消息队列串行化
- 请求入队，单消费者处理
- 避免并发竞争
- 削峰填谷

### 5. 库存预扣
- 下单时预扣库存
- 支付超时释放
- 提升用户体验

## 面试要点
1. **锁的选择**：根据并发量和一致性要求选择
2. **性能权衡**：一致性vs性能的平衡
3. **系统复杂度**：方案的实现难度和维护成本