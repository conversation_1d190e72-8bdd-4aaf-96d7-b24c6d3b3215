# 分布式ID生成系统

## 需求分析
- **全局唯一**：在分布式环境下保证ID唯一性
- **高性能**：支持高并发ID生成
- **有序性**：ID趋势递增，便于数据库索引
- **高可用**：服务高可用，避免单点故障

## 主流方案对比

### 1. UUID
```go
import "github.com/google/uuid"

func generateUUID() string {
    return uuid.New().String()
}
```
- **优点**：简单、本地生成、性能高
- **缺点**：无序、占用空间大、不适合做主键

### 2. 数据库自增ID
```sql
CREATE TABLE id_generator (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stub CHAR(1) NOT NULL DEFAULT ''
) ENGINE=MyISAM;

INSERT INTO id_generator (stub) VALUES ('a');
SELECT LAST_INSERT_ID();
```
- **优点**：简单、有序、唯一
- **缺点**：性能瓶颈、单点故障

### 3. 雪花算法(Snowflake)
```go
type Snowflake struct {
    mutex       sync.Mutex
    timestamp   int64
    machineID   int64
    sequence    int64
}

func (s *Snowflake) Generate() int64 {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    now := time.Now().UnixNano() / 1000000
    if now == s.timestamp {
        s.sequence = (s.sequence + 1) & 4095 // 12位序列号
        if s.sequence == 0 {
            // 等待下一毫秒
            for now <= s.timestamp {
                now = time.Now().UnixNano() / 1000000
            }
        }
    } else {
        s.sequence = 0
    }
    
    s.timestamp = now
    return ((now - 1609459200000) << 22) | (s.machineID << 12) | s.sequence
}
```

**雪花算法结构**：
- 1位符号位(固定0)
- 41位时间戳(毫秒级)
- 10位机器ID
- 12位序列号

### 4. 号段模式
```go
type SegmentIDGenerator struct {
    currentID   int64
    maxID       int64
    step        int64
    businessTag string
}

func (g *SegmentIDGenerator) NextID() (int64, error) {
    if g.currentID >= g.maxID {
        // 从数据库获取新的号段
        if err := g.loadNextSegment(); err != nil {
            return 0, err
        }
    }
    
    g.currentID++
    return g.currentID, nil
}
```

### 5. Redis生成ID
```go
func generateIDFromRedis(key string) (int64, error) {
    return rdb.Incr(ctx, key).Result()
}
```

## 系统架构设计

### 1. 多机房部署
- 不同机房分配不同的机器ID范围
- 避免机器ID冲突
- 提高系统可用性

### 2. 服务注册与发现
- 使用Consul/Etcd管理机器ID分配
- 自动故障检测和恢复
- 动态扩容支持

### 3. 监控告警
- ID生成QPS监控
- 服务可用性监控
- 机器ID冲突检测

## 面试要点
1. **方案选择**：根据业务场景选择合适的方案
2. **时钟回拨**：雪花算法如何处理时钟回拨问题
3. **机器ID分配**：如何保证机器ID不冲突
4. **性能优化**：如何提高ID生成性能
