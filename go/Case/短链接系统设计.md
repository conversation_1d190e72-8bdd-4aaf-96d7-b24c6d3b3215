# 短链接系统设计

## 需求分析
- **功能需求**：长链接转短链接、短链接跳转、统计分析
- **非功能需求**：高并发、低延迟、高可用、数据一致性

## 系统架构

### 1. 核心组件
- **短链生成服务**：负责生成唯一短链
- **跳转服务**：处理短链访问和重定向
- **统计服务**：记录访问数据和分析
- **缓存层**：Redis缓存热点数据

### 2. 短链生成算法

**方案一：自增ID + Base62编码**
```go
func generateShortURL(id int64) string {
    const base62 = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    if id == 0 {
        return "0"
    }
    
    var result []byte
    for id > 0 {
        result = append([]byte{base62[id%62]}, result...)
        id /= 62
    }
    return string(result)
}
```

**方案二：哈希算法**
```go
func generateByHash(longURL string) string {
    hash := md5.Sum([]byte(longURL))
    return base64.URLEncoding.EncodeToString(hash[:])[:6]
}
```

### 3. 数据存储设计

**MySQL表结构**
```sql
CREATE TABLE short_urls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    short_code VARCHAR(10) UNIQUE NOT NULL,
    long_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    click_count INT DEFAULT 0,
    INDEX idx_short_code (short_code)
);
```

### 4. 缓存策略
- **热点数据**：访问频繁的短链缓存到Redis
- **缓存更新**：写入时更新缓存，设置合理过期时间
- **缓存穿透**：布隆过滤器防止无效查询

### 5. 高并发优化
- **读写分离**：读请求走从库，写请求走主库
- **分库分表**：按短链hash值分片
- **异步统计**：点击统计异步更新，避免影响跳转性能

## 面试要点
1. **唯一性保证**：如何确保短链不重复
2. **性能优化**：缓存策略、数据库优化
3. **扩展性**：如何支持更大规模的访问量
4. **数据一致性**：缓存和数据库的一致性问题
