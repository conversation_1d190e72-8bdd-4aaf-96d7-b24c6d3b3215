# 秒杀系统设计与实现

## 核心挑战
1. **高并发**：短时间内大量请求涌入
2. **超卖问题**：库存控制的原子性
3. **系统稳定性**：避免服务崩溃
4. **用户体验**：快速响应

## 系统架构设计

### 1. 分层限流
- **前端限流**：按钮置灰、验证码、防重复提交
- **网关限流**：Nginx限流、API网关限流
- **应用限流**：令牌桶、漏桶算法

### 2. 缓存预热
- 提前将商品信息、库存加载到Redis
- 使用Redis原子操作(DECR)扣减库存
- 设置合理的缓存过期策略

### 3. 异步处理
- 消息队列削峰填谷(RabbitMQ/Kafka)
- 订单异步生成，提升响应速度
- 库存预扣，支付超时释放

## 防超卖方案

### 1. Redis原子操作
```go
// 使用Redis DECR原子扣减
stock := rdb.Decr(ctx, "stock:"+productID).Val()
if stock < 0 {
    return errors.New("库存不足")
}
```

### 2. 数据库乐观锁
```sql
UPDATE products
SET stock = stock - 1, version = version + 1
WHERE id = ? AND version = ? AND stock > 0
```

### 3. 分布式锁
```go
// Redis分布式锁
lockKey := "lock:product:" + productID
acquired := rdb.SetNX(ctx, lockKey, "1", 10*time.Second).Val()
if !acquired {
    return errors.New("请稍后重试")
}
defer rdb.Del(ctx, lockKey)
```

## 面试要点
1. **如何防止超卖？** Redis原子操作、数据库锁、分布式锁
2. **如何应对高并发？** 多层限流、缓存、异步处理
3. **如何保证一致性？** 事务、补偿机制、最终一致性