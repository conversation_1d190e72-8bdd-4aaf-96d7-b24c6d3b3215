HTTP 1.0、1.1 和 2.0 是三个不同版本的超文本传输协议（HTTP），它们之间有一些关键的区别，主要体现在连接管理、性能优化和功能扩展等方面。

### HTTP 1.0
- **单次请求-响应**：HTTP 1.0 是无状态协议，每次请求都会建立一个新的TCP连接，服务器处理完请求后立即关闭连接。
- **请求头简化**：HTTP 1.0 请求头较为简单，不支持Host头部字段，因此不能直接支持虚拟主机。
- **缓存控制**：HTTP 1.0 引入了简单的缓存控制机制，如通过 `Expires` 头部指定资源的过期时间。

### HTTP 1.1
- **持久连接**：HTTP 1.1 默认支持持久连接（Persistent Connection），即 `Connection: keep-alive`。多个请求可以复用同一个TCP连接，从而减少连接的建立和断开带来的开销。
- **管道机制**：HTTP 1.1 引入了请求管线化（Pipelining），允许客户端在收到第一个响应前发送多个请求。但由于队头阻塞（Head-of-Line Blocking）问题，这个特性并没有得到广泛应用。
- **Host头**：HTTP 1.1 要求所有请求必须包含Host头部字段，支持同一服务器上多个域名的虚拟主机。
- **缓存控制增强**：引入了 `Cache-Control` 头部字段，提供更细粒度的缓存控制策略。
- **分块传输编码**：支持分块传输编码（Chunked Transfer Encoding），允许服务器在数据尚未全部准备好时开始传输，适用于流式数据传输。

### HTTP 2.0
- **二进制协议**：HTTP 2.0 将HTTP消息封装为二进制帧（Binary Framing Layer），而不是文本协议。这种改进使得协议更加紧凑、高效，减少了解析开销。
- **多路复用**：HTTP 2.0 支持多路复用（Multiplexing），允许在一个TCP连接上同时发送多个请求和响应，消除了HTTP 1.x中的队头阻塞问题。
- **头部压缩**：HTTP 2.0 引入了HPACK头部压缩算法，大大减少了请求和响应中的头部大小，提升了传输效率。
- **服务器推送**：HTTP 2.0 支持服务器推送（Server Push），允许服务器在客户端请求前主动推送资源，减少延迟。
- **优先级**：HTTP 2.0 允许客户端为请求设置优先级，服务器可以根据优先级优化资源传输顺序。

### 总结
- **HTTP 1.0**：每次请求都需要建立和关闭连接，效率较低，功能简单。
- **HTTP 1.1**：引入了持久连接和请求管线化，优化了连接管理和缓存控制，增加了对虚拟主机的支持。
- **HTTP 2.0**：采用二进制传输，多路复用，头部压缩和服务器推送等新特性，显著提升了传输效率和用户体验。