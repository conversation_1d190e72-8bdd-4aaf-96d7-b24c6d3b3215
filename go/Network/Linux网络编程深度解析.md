# Linux网络编程深度解析

## Socket编程基础

### 1. Socket基本概念

#### Socket类型
```c
#include <sys/socket.h>

// TCP Socket (流式套接字)
int tcp_socket = socket(AF_INET, SOCK_STREAM, 0);

// UDP Socket (数据报套接字)
int udp_socket = socket(AF_INET, SOCK_DGRAM, 0);

// Unix域套接字
int unix_socket = socket(AF_UNIX, SOCK_STREAM, 0);

// 原始套接字（需要root权限）
int raw_socket = socket(AF_INET, SOCK_RAW, IPPROTO_ICMP);
```

#### 地址结构
```c
#include <netinet/in.h>
#include <sys/un.h>

// IPv4地址结构
struct sockaddr_in {
    sa_family_t sin_family;     // AF_INET
    in_port_t sin_port;         // 端口号（网络字节序）
    struct in_addr sin_addr;    // IP地址
    char sin_zero[8];           // 填充字节
};

// IPv6地址结构
struct sockaddr_in6 {
    sa_family_t sin6_family;    // AF_INET6
    in_port_t sin6_port;        // 端口号
    uint32_t sin6_flowinfo;     // 流信息
    struct in6_addr sin6_addr;  // IPv6地址
    uint32_t sin6_scope_id;     // 作用域ID
};

// Unix域套接字地址
struct sockaddr_un {
    sa_family_t sun_family;     // AF_UNIX
    char sun_path[108];         // 路径名
};
```

### 2. TCP服务器编程

#### 基本TCP服务器
```c
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <string.h>

int create_tcp_server(int port) {
    int server_fd;
    struct sockaddr_in address;
    int opt = 1;

    // 创建socket
    if ((server_fd = socket(AF_INET, SOCK_STREAM, 0)) == 0) {
        perror("socket failed");
        return -1;
    }

    // 设置socket选项，允许地址重用
    if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR,
                   &opt, sizeof(opt))) {
        perror("setsockopt");
        close(server_fd);
        return -1;
    }

    // 设置地址结构
    address.sin_family = AF_INET;
    address.sin_addr.s_addr = INADDR_ANY;
    address.sin_port = htons(port);

    // 绑定地址
    if (bind(server_fd, (struct sockaddr *)&address,
             sizeof(address)) < 0) {
        perror("bind failed");
        close(server_fd);
        return -1;
    }

    // 开始监听
    if (listen(server_fd, 10) < 0) {
        perror("listen");
        close(server_fd);
        return -1;
    }

    printf("TCP服务器监听端口 %d\n", port);
    return server_fd;
}

void handle_tcp_client(int client_fd) {
    char buffer[1024] = {0};
    ssize_t bytes_read;

    while ((bytes_read = read(client_fd, buffer, sizeof(buffer))) > 0) {
        printf("收到数据: %s\n", buffer);

        // 回显数据
        if (write(client_fd, buffer, bytes_read) != bytes_read) {
            perror("write");
            break;
        }

        memset(buffer, 0, sizeof(buffer));
    }

    if (bytes_read == 0) {
        printf("客户端断开连接\n");
    } else {
        perror("read");
    }

    close(client_fd);
}

void run_tcp_server(int port) {
    int server_fd = create_tcp_server(port);
    if (server_fd == -1) return;

    while (1) {
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);

        int client_fd = accept(server_fd, (struct sockaddr *)&client_addr,
                              &client_len);
        if (client_fd < 0) {
            perror("accept");
            continue;
        }

        printf("新客户端连接: %s:%d\n",
               inet_ntoa(client_addr.sin_addr),
               ntohs(client_addr.sin_port));

        // 创建子进程处理客户端
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程
            close(server_fd);
            handle_tcp_client(client_fd);
            exit(0);
        } else if (pid > 0) {
            // 父进程
            close(client_fd);
        } else {
            perror("fork");
        }
    }

    close(server_fd);
}
```

### 3. TCP客户端编程

#### 基本TCP客户端
```c
int create_tcp_client(const char* server_ip, int port) {
    int sock_fd;
    struct sockaddr_in server_addr;

    // 创建socket
    if ((sock_fd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        perror("socket creation error");
        return -1;
    }

    // 设置服务器地址
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);

    // 转换IP地址
    if (inet_pton(AF_INET, server_ip, &server_addr.sin_addr) <= 0) {
        printf("Invalid address/ Address not supported\n");
        close(sock_fd);
        return -1;
    }

    // 连接服务器
    if (connect(sock_fd, (struct sockaddr *)&server_addr,
                sizeof(server_addr)) < 0) {
        perror("Connection Failed");
        close(sock_fd);
        return -1;
    }

    printf("连接到服务器 %s:%d\n", server_ip, port);
    return sock_fd;
}

void tcp_client_communication(const char* server_ip, int port) {
    int sock_fd = create_tcp_client(server_ip, port);
    if (sock_fd == -1) return;

    char buffer[1024];

    while (1) {
        printf("输入消息 (输入'quit'退出): ");
        if (fgets(buffer, sizeof(buffer), stdin) == NULL) {
            break;
        }

        // 移除换行符
        buffer[strcspn(buffer, "\n")] = 0;

        if (strcmp(buffer, "quit") == 0) {
            break;
        }

        // 发送数据
        if (send(sock_fd, buffer, strlen(buffer), 0) < 0) {
            perror("send");
            break;
        }

        // 接收响应
        ssize_t bytes_received = recv(sock_fd, buffer, sizeof(buffer) - 1, 0);
        if (bytes_received > 0) {
            buffer[bytes_received] = '\0';
            printf("服务器响应: %s\n", buffer);
        } else if (bytes_received == 0) {
            printf("服务器断开连接\n");
            break;
        } else {
            perror("recv");
            break;
        }
    }

    close(sock_fd);
}
```

## I/O多路复用

### 1. select()模型

#### select基本使用
```c
#include <sys/select.h>

void select_server_example(int port) {
    int server_fd = create_tcp_server(port);
    if (server_fd == -1) return;

    fd_set master_fds, read_fds;
    int max_fd = server_fd;

    // 初始化文件描述符集合
    FD_ZERO(&master_fds);
    FD_SET(server_fd, &master_fds);

    printf("Select服务器启动，端口: %d\n", port);

    while (1) {
        read_fds = master_fds;

        // 等待文件描述符就绪
        int activity = select(max_fd + 1, &read_fds, NULL, NULL, NULL);

        if (activity < 0) {
            perror("select error");
            break;
        }

        // 检查所有文件描述符
        for (int fd = 0; fd <= max_fd; fd++) {
            if (FD_ISSET(fd, &read_fds)) {
                if (fd == server_fd) {
                    // 新连接
                    struct sockaddr_in client_addr;
                    socklen_t client_len = sizeof(client_addr);

                    int client_fd = accept(server_fd,
                                         (struct sockaddr *)&client_addr,
                                         &client_len);
                    if (client_fd < 0) {
                        perror("accept");
                        continue;
                    }

                    printf("新连接: %s:%d (fd=%d)\n",
                           inet_ntoa(client_addr.sin_addr),
                           ntohs(client_addr.sin_port), client_fd);

                    // 添加到监控集合
                    FD_SET(client_fd, &master_fds);
                    if (client_fd > max_fd) {
                        max_fd = client_fd;
                    }
                } else {
                    // 客户端数据
                    char buffer[1024];
                    ssize_t bytes_read = read(fd, buffer, sizeof(buffer));

                    if (bytes_read <= 0) {
                        // 连接关闭或错误
                        if (bytes_read == 0) {
                            printf("客户端 fd=%d 断开连接\n", fd);
                        } else {
                            perror("read");
                        }

                        close(fd);
                        FD_CLR(fd, &master_fds);
                    } else {
                        // 处理数据
                        buffer[bytes_read] = '\0';
                        printf("fd=%d 发送: %s\n", fd, buffer);

                        // 回显给所有客户端
                        for (int i = 0; i <= max_fd; i++) {
                            if (FD_ISSET(i, &master_fds) && i != server_fd && i != fd) {
                                if (send(i, buffer, bytes_read, 0) == -1) {
                                    perror("send");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    close(server_fd);
}
```

### 2. poll()模型

#### poll基本使用
```c
#include <poll.h>

#define MAX_CLIENTS 100

void poll_server_example(int port) {
    int server_fd = create_tcp_server(port);
    if (server_fd == -1) return;

    struct pollfd fds[MAX_CLIENTS];
    int nfds = 1;

    // 初始化poll结构
    fds[0].fd = server_fd;
    fds[0].events = POLLIN;

    for (int i = 1; i < MAX_CLIENTS; i++) {
        fds[i].fd = -1;
    }

    printf("Poll服务器启动，端口: %d\n", port);

    while (1) {
        int poll_count = poll(fds, nfds, -1);

        if (poll_count < 0) {
            perror("poll");
            break;
        }

        // 检查服务器socket
        if (fds[0].revents & POLLIN) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);

            int client_fd = accept(server_fd,
                                 (struct sockaddr *)&client_addr,
                                 &client_len);
            if (client_fd < 0) {
                perror("accept");
                continue;
            }

            printf("新连接: %s:%d (fd=%d)\n",
                   inet_ntoa(client_addr.sin_addr),
                   ntohs(client_addr.sin_port), client_fd);

            // 添加到poll数组
            int i;
            for (i = 1; i < MAX_CLIENTS; i++) {
                if (fds[i].fd == -1) {
                    fds[i].fd = client_fd;
                    fds[i].events = POLLIN;
                    break;
                }
            }

            if (i == MAX_CLIENTS) {
                printf("达到最大客户端数量\n");
                close(client_fd);
            } else if (i >= nfds) {
                nfds = i + 1;
            }
        }

        // 检查客户端socket
        for (int i = 1; i < nfds; i++) {
            if (fds[i].fd == -1) continue;

            if (fds[i].revents & POLLIN) {
                char buffer[1024];
                ssize_t bytes_read = read(fds[i].fd, buffer, sizeof(buffer));

                if (bytes_read <= 0) {
                    if (bytes_read == 0) {
                        printf("客户端 fd=%d 断开连接\n", fds[i].fd);
                    } else {
                        perror("read");
                    }

                    close(fds[i].fd);
                    fds[i].fd = -1;
                } else {
                    buffer[bytes_read] = '\0';
                    printf("fd=%d 发送: %s\n", fds[i].fd, buffer);

                    // 回显数据
                    if (send(fds[i].fd, buffer, bytes_read, 0) == -1) {
                        perror("send");
                    }
                }
            }
        }
    }

    close(server_fd);
}
```

### 3. epoll()模型

#### epoll基本使用
```c
#include <sys/epoll.h>

#define MAX_EVENTS 100

void epoll_server_example(int port) {
    int server_fd = create_tcp_server(port);
    if (server_fd == -1) return;

    // 创建epoll实例
    int epoll_fd = epoll_create1(0);
    if (epoll_fd == -1) {
        perror("epoll_create1");
        close(server_fd);
        return;
    }

    // 添加服务器socket到epoll
    struct epoll_event ev, events[MAX_EVENTS];
    ev.events = EPOLLIN;
    ev.data.fd = server_fd;

    if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, server_fd, &ev) == -1) {
        perror("epoll_ctl: server_fd");
        close(server_fd);
        close(epoll_fd);
        return;
    }

    printf("Epoll服务器启动，端口: %d\n", port);

    while (1) {
        int nfds = epoll_wait(epoll_fd, events, MAX_EVENTS, -1);

        if (nfds == -1) {
            perror("epoll_wait");
            break;
        }

        for (int i = 0; i < nfds; i++) {
            if (events[i].data.fd == server_fd) {
                // 新连接
                struct sockaddr_in client_addr;
                socklen_t client_len = sizeof(client_addr);

                int client_fd = accept(server_fd,
                                     (struct sockaddr *)&client_addr,
                                     &client_len);
                if (client_fd == -1) {
                    perror("accept");
                    continue;
                }

                printf("新连接: %s:%d (fd=%d)\n",
                       inet_ntoa(client_addr.sin_addr),
                       ntohs(client_addr.sin_port), client_fd);

                // 设置非阻塞
                int flags = fcntl(client_fd, F_GETFL, 0);
                fcntl(client_fd, F_SETFL, flags | O_NONBLOCK);

                // 添加到epoll
                ev.events = EPOLLIN | EPOLLET; // 边缘触发
                ev.data.fd = client_fd;
                if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, client_fd, &ev) == -1) {
                    perror("epoll_ctl: client_fd");
                    close(client_fd);
                }
            } else {
                // 客户端数据
                int client_fd = events[i].data.fd;
                char buffer[1024];

                while (1) {
                    ssize_t bytes_read = read(client_fd, buffer, sizeof(buffer));

                    if (bytes_read > 0) {
                        buffer[bytes_read] = '\0';
                        printf("fd=%d 发送: %s\n", client_fd, buffer);

                        // 回显数据
                        if (send(client_fd, buffer, bytes_read, 0) == -1) {
                            if (errno != EAGAIN) {
                                perror("send");
                            }
                        }
                    } else if (bytes_read == 0) {
                        // 连接关闭
                        printf("客户端 fd=%d 断开连接\n", client_fd);
                        epoll_ctl(epoll_fd, EPOLL_CTL_DEL, client_fd, NULL);
                        close(client_fd);
                        break;
                    } else {
                        if (errno == EAGAIN) {
                            // 数据读完了
                            break;
                        } else {
                            perror("read");
                            epoll_ctl(epoll_fd, EPOLL_CTL_DEL, client_fd, NULL);
                            close(client_fd);
                            break;
                        }
                    }
                }
            }
        }
    }

    close(server_fd);
    close(epoll_fd);
}
```