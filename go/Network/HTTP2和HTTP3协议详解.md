HTTP/2和HTTP/3是现代Web通信的重要协议，它们在性能、安全性和效率方面相比HTTP/1.1有了显著提升。理解这些协议的工作原理和特性对于构建高性能Web应用至关重要。

### 1. **HTTP/2协议详解**

#### **HTTP/2的核心特性**
- **二进制分帧**：使用二进制格式而非文本格式
- **多路复用**：单个连接上并行处理多个请求
- **头部压缩**：使用HPACK算法压缩HTTP头部
- **服务器推送**：服务器主动推送资源给客户端
- **流优先级**：可以设置请求的优先级

#### **HTTP/2帧结构**
```
HTTP/2 Frame Format:
+-----------------------------------------------+
|                 Length (24)                   |
+---------------+---------------+---------------+
|   Type (8)    |   Flags (8)   |
+-+-------------+---------------+-------------------------------+
|R|                 Stream Identifier (31)                      |
+=+=============================================================+
|                   Frame Payload (0...)                      ...
+---------------------------------------------------------------+
```

### 2. **Go语言HTTP/2实现**

#### **HTTP/2服务器实现**
```go
package main

import (
    "crypto/tls"
    "fmt"
    "log"
    "net/http"
    "time"
    
    "golang.org/x/net/http2"
)

type HTTP2Server struct {
    server   *http.Server
    handler  *HTTP2Handler
    certFile string
    keyFile  string
}

type HTTP2Handler struct {
    pushEnabled bool
}

func NewHTTP2Server(addr, certFile, keyFile string) *HTTP2Server {
    handler := &HTTP2Handler{
        pushEnabled: true,
    }
    
    server := &http.Server{
        Addr:         addr,
        Handler:      handler,
        TLSConfig:    &tls.Config{},
        ReadTimeout:  30 * time.Second,
        WriteTimeout: 30 * time.Second,
        IdleTimeout:  120 * time.Second,
    }
    
    // 配置HTTP/2
    http2.ConfigureServer(server, &http2.Server{
        MaxConcurrentStreams:         1000,
        MaxReadFrameSize:            1048576, // 1MB
        IdleTimeout:                 300 * time.Second,
        MaxUploadBufferPerConnection: 1048576,
        MaxUploadBufferPerStream:     1048576,
    })
    
    return &HTTP2Server{
        server:   server,
        handler:  handler,
        certFile: certFile,
        keyFile:  keyFile,
    }
}

func (h *HTTP2Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
    // 检查是否支持HTTP/2
    if r.ProtoMajor == 2 {
        w.Header().Set("X-Protocol", "HTTP/2")
    }
    
    switch r.URL.Path {
    case "/":
        h.handleIndex(w, r)
    case "/api/data":
        h.handleAPIData(w, r)
    case "/stream":
        h.handleStream(w, r)
    default:
        http.NotFound(w, r)
    }
}

func (h *HTTP2Handler) handleIndex(w http.ResponseWriter, r *http.Request) {
    // 服务器推送示例
    if h.pushEnabled {
        if pusher, ok := w.(http.Pusher); ok {
            // 推送CSS文件
            if err := pusher.Push("/static/style.css", nil); err != nil {
                log.Printf("Failed to push CSS: %v", err)
            }
            
            // 推送JavaScript文件
            if err := pusher.Push("/static/app.js", nil); err != nil {
                log.Printf("Failed to push JS: %v", err)
            }
        }
    }
    
    html := `
    <!DOCTYPE html>
    <html>
    <head>
        <title>HTTP/2 Demo</title>
        <link rel="stylesheet" href="/static/style.css">
    </head>
    <body>
        <h1>HTTP/2 Server Push Demo</h1>
        <script src="/static/app.js"></script>
    </body>
    </html>
    `
    
    w.Header().Set("Content-Type", "text/html")
    w.Write([]byte(html))
}

func (h *HTTP2Handler) handleAPIData(w http.ResponseWriter, r *http.Request) {
    // 模拟API响应
    data := map[string]interface{}{
        "timestamp": time.Now().Unix(),
        "protocol":  r.Proto,
        "method":    r.Method,
        "headers":   r.Header,
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(data)
}

// 服务器发送事件（SSE）示例
func (h *HTTP2Handler) handleStream(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "text/event-stream")
    w.Header().Set("Cache-Control", "no-cache")
    w.Header().Set("Connection", "keep-alive")
    
    flusher, ok := w.(http.Flusher)
    if !ok {
        http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
        return
    }
    
    // 发送数据流
    for i := 0; i < 10; i++ {
        fmt.Fprintf(w, "data: Message %d at %s\n\n", i, time.Now().Format(time.RFC3339))
        flusher.Flush()
        time.Sleep(time.Second)
    }
}

func (s *HTTP2Server) Start() error {
    log.Printf("Starting HTTP/2 server on %s", s.server.Addr)
    return s.server.ListenAndServeTLS(s.certFile, s.keyFile)
}
```

#### **HTTP/2客户端实现**
```go
type HTTP2Client struct {
    client   *http.Client
    baseURL  string
}

func NewHTTP2Client(baseURL string) *HTTP2Client {
    // 配置支持HTTP/2的客户端
    transport := &http2.Transport{
        TLSClientConfig: &tls.Config{
            InsecureSkipVerify: true, // 仅用于测试
        },
        AllowHTTP: false,
        MaxHeaderListSize: 10 << 20, // 10MB
    }
    
    client := &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
    
    return &HTTP2Client{
        client:  client,
        baseURL: baseURL,
    }
}

// 并发请求示例（利用HTTP/2多路复用）
func (c *HTTP2Client) ConcurrentRequests(urls []string) ([]Response, error) {
    var wg sync.WaitGroup
    responses := make([]Response, len(urls))
    errors := make([]error, len(urls))
    
    for i, url := range urls {
        wg.Add(1)
        go func(index int, requestURL string) {
            defer wg.Done()
            
            fullURL := c.baseURL + requestURL
            resp, err := c.client.Get(fullURL)
            if err != nil {
                errors[index] = err
                return
            }
            defer resp.Body.Close()
            
            body, err := ioutil.ReadAll(resp.Body)
            if err != nil {
                errors[index] = err
                return
            }
            
            responses[index] = Response{
                StatusCode: resp.StatusCode,
                Headers:    resp.Header,
                Body:       body,
                Protocol:   resp.Proto,
            }
        }(i, url)
    }
    
    wg.Wait()
    
    // 检查错误
    for _, err := range errors {
        if err != nil {
            return nil, err
        }
    }
    
    return responses, nil
}

type Response struct {
    StatusCode int
    Headers    http.Header
    Body       []byte
    Protocol   string
}

// 流式请求处理
func (c *HTTP2Client) StreamRequest(url string, handler func([]byte) error) error {
    resp, err := c.client.Get(c.baseURL + url)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    scanner := bufio.NewScanner(resp.Body)
    for scanner.Scan() {
        line := scanner.Bytes()
        if err := handler(line); err != nil {
            return err
        }
    }
    
    return scanner.Err()
}
```

### 3. **HTTP/3协议详解**

#### **HTTP/3的核心特性**
- **基于QUIC协议**：使用UDP而非TCP作为传输层
- **内置TLS加密**：默认加密，减少握手时间
- **连接迁移**：支持IP地址变化时的连接保持
- **改进的拥塞控制**：更好的网络适应性
- **头部压缩**：使用QPACK算法

#### **QUIC协议特点**
```
QUIC Connection:
Client                                Server
  |                                     |
  |-------- Initial Packet ----------->|
  |<------- Initial Packet ------------|
  |                                     |
  |-------- Handshake Packet --------->|
  |<------- Handshake Packet ----------|
  |                                     |
  |-------- 1-RTT Packet ------------->|
  |<------- 1-RTT Packet --------------|
```

### 4. **Go语言HTTP/3实现**

#### **HTTP/3服务器实现**
```go
package main

import (
    "context"
    "crypto/tls"
    "log"
    "net/http"
    "time"
    
    "github.com/lucas-clemente/quic-go/http3"
)

type HTTP3Server struct {
    server   *http3.Server
    handler  *HTTP3Handler
    certFile string
    keyFile  string
}

type HTTP3Handler struct {
    // HTTP/3特定的处理逻辑
}

func NewHTTP3Server(addr, certFile, keyFile string) *HTTP3Server {
    handler := &HTTP3Handler{}
    
    server := &http3.Server{
        Addr:      addr,
        Handler:   handler,
        TLSConfig: &tls.Config{},
        QuicConfig: &quic.Config{
            MaxIdleTimeout:        300 * time.Second,
            MaxIncomingStreams:    1000,
            MaxIncomingUniStreams: 1000,
            KeepAlive:            true,
        },
    }
    
    return &HTTP3Server{
        server:   server,
        handler:  handler,
        certFile: certFile,
        keyFile:  keyFile,
    }
}

func (h *HTTP3Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
    // 设置HTTP/3特定的响应头
    w.Header().Set("X-Protocol", "HTTP/3")
    w.Header().Set("Alt-Svc", `h3=":443"; ma=86400`)
    
    switch r.URL.Path {
    case "/":
        h.handleIndex(w, r)
    case "/api/data":
        h.handleAPIData(w, r)
    case "/upload":
        h.handleUpload(w, r)
    default:
        http.NotFound(w, r)
    }
}

func (h *HTTP3Handler) handleIndex(w http.ResponseWriter, r *http.Request) {
    html := `
    <!DOCTYPE html>
    <html>
    <head>
        <title>HTTP/3 Demo</title>
    </head>
    <body>
        <h1>HTTP/3 Server</h1>
        <p>Protocol: ` + r.Proto + `</p>
        <p>Connection: QUIC over UDP</p>
    </body>
    </html>
    `
    
    w.Header().Set("Content-Type", "text/html")
    w.Write([]byte(html))
}

func (h *HTTP3Handler) handleAPIData(w http.ResponseWriter, r *http.Request) {
    data := map[string]interface{}{
        "timestamp": time.Now().Unix(),
        "protocol":  r.Proto,
        "quic":      true,
        "encrypted": true,
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(data)
}

// 处理大文件上传（利用QUIC的流特性）
func (h *HTTP3Handler) handleUpload(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }
    
    // 解析multipart form
    err := r.ParseMultipartForm(32 << 20) // 32MB
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    
    file, header, err := r.FormFile("file")
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    defer file.Close()
    
    // 处理文件上传
    log.Printf("Received file: %s, size: %d bytes", header.Filename, header.Size)
    
    // 流式处理文件内容
    buffer := make([]byte, 8192)
    totalBytes := int64(0)
    
    for {
        n, err := file.Read(buffer)
        if err == io.EOF {
            break
        }
        if err != nil {
            http.Error(w, err.Error(), http.StatusInternalServerError)
            return
        }
        
        totalBytes += int64(n)
        // 处理文件块
    }
    
    response := map[string]interface{}{
        "filename":    header.Filename,
        "size":        totalBytes,
        "protocol":    r.Proto,
        "upload_time": time.Now().Unix(),
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

func (s *HTTP3Server) Start() error {
    log.Printf("Starting HTTP/3 server on %s", s.server.Addr)
    return s.server.ListenAndServeTLS(s.certFile, s.keyFile)
}
```

#### **HTTP/3客户端实现**
```go
type HTTP3Client struct {
    client  *http.Client
    baseURL string
}

func NewHTTP3Client(baseURL string) *HTTP3Client {
    // 配置HTTP/3客户端
    roundTripper := &http3.RoundTripper{
        TLSClientConfig: &tls.Config{
            InsecureSkipVerify: true, // 仅用于测试
        },
        QuicConfig: &quic.Config{
            MaxIdleTimeout: 300 * time.Second,
            KeepAlive:     true,
        },
    }
    
    client := &http.Client{
        Transport: roundTripper,
        Timeout:   30 * time.Second,
    }
    
    return &HTTP3Client{
        client:  client,
        baseURL: baseURL,
    }
}

// 测试连接迁移
func (c *HTTP3Client) TestConnectionMigration() error {
    // 发送初始请求
    resp1, err := c.client.Get(c.baseURL + "/api/data")
    if err != nil {
        return err
    }
    resp1.Body.Close()
    
    log.Printf("Initial request: %s", resp1.Proto)
    
    // 模拟网络切换（实际场景中可能是WiFi到4G的切换）
    time.Sleep(time.Second * 2)
    
    // 发送后续请求（QUIC应该能够保持连接）
    resp2, err := c.client.Get(c.baseURL + "/api/data")
    if err != nil {
        return err
    }
    resp2.Body.Close()
    
    log.Printf("After migration: %s", resp2.Proto)
    
    return nil
}

// 并行流处理
func (c *HTTP3Client) ParallelStreams(urls []string) error {
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 10) // 限制并发数
    
    for _, url := range urls {
        wg.Add(1)
        go func(requestURL string) {
            defer wg.Done()
            
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            resp, err := c.client.Get(c.baseURL + requestURL)
            if err != nil {
                log.Printf("Request failed: %v", err)
                return
            }
            defer resp.Body.Close()
            
            log.Printf("Response from %s: %s", requestURL, resp.Proto)
        }(url)
    }
    
    wg.Wait()
    return nil
}
```

### 5. **协议性能对比**

#### **性能测试实现**
```go
type ProtocolBenchmark struct {
    http1Client *http.Client
    http2Client *http.Client
    http3Client *http.Client
}

func NewProtocolBenchmark() *ProtocolBenchmark {
    return &ProtocolBenchmark{
        http1Client: &http.Client{
            Transport: &http.Transport{
                MaxIdleConns:        100,
                MaxIdleConnsPerHost: 10,
                IdleConnTimeout:     90 * time.Second,
            },
            Timeout: 30 * time.Second,
        },
        http2Client: NewHTTP2Client("").client,
        http3Client: NewHTTP3Client("").client,
    }
}

func (pb *ProtocolBenchmark) BenchmarkProtocols(urls []string, iterations int) {
    protocols := map[string]*http.Client{
        "HTTP/1.1": pb.http1Client,
        "HTTP/2":   pb.http2Client,
        "HTTP/3":   pb.http3Client,
    }
    
    for protocol, client := range protocols {
        duration := pb.benchmarkClient(client, urls, iterations)
        log.Printf("%s: %v for %d requests", protocol, duration, len(urls)*iterations)
    }
}

func (pb *ProtocolBenchmark) benchmarkClient(client *http.Client, 
    urls []string, iterations int) time.Duration {
    
    start := time.Now()
    
    for i := 0; i < iterations; i++ {
        var wg sync.WaitGroup
        for _, url := range urls {
            wg.Add(1)
            go func(requestURL string) {
                defer wg.Done()
                
                resp, err := client.Get(requestURL)
                if err != nil {
                    return
                }
                defer resp.Body.Close()
                
                // 读取响应体
                io.Copy(ioutil.Discard, resp.Body)
            }(url)
        }
        wg.Wait()
    }
    
    return time.Since(start)
}

// 延迟测试
func (pb *ProtocolBenchmark) MeasureLatency(url string, count int) map[string]time.Duration {
    results := make(map[string]time.Duration)
    
    protocols := map[string]*http.Client{
        "HTTP/1.1": pb.http1Client,
        "HTTP/2":   pb.http2Client,
        "HTTP/3":   pb.http3Client,
    }
    
    for protocol, client := range protocols {
        var totalLatency time.Duration
        
        for i := 0; i < count; i++ {
            start := time.Now()
            
            resp, err := client.Get(url)
            if err != nil {
                continue
            }
            resp.Body.Close()
            
            totalLatency += time.Since(start)
        }
        
        results[protocol] = totalLatency / time.Duration(count)
    }
    
    return results
}
```

### 6. **面试常见问题**

#### **问题1：HTTP/2相比HTTP/1.1的优势**
- **多路复用**：解决队头阻塞问题
- **头部压缩**：减少传输开销
- **服务器推送**：主动推送资源
- **二进制协议**：更高效的解析

#### **问题2：HTTP/3相比HTTP/2的改进**
- **基于UDP**：避免TCP的队头阻塞
- **连接迁移**：网络切换时保持连接
- **更快的握手**：0-RTT连接建立
- **改进的拥塞控制**：更好的网络适应性

#### **问题3：何时选择不同的HTTP版本**
- **HTTP/1.1**：简单应用，兼容性要求高
- **HTTP/2**：现代Web应用，需要多路复用
- **HTTP/3**：移动应用，网络环境不稳定

### 总结

HTTP/2和HTTP/3代表了Web协议的发展方向：

1. **HTTP/2**：解决了HTTP/1.1的性能问题，广泛应用于现代Web
2. **HTTP/3**：基于QUIC协议，进一步提升了性能和可靠性
3. **选择策略**：根据应用场景和网络环境选择合适的协议版本

在实际开发中，需要考虑：
- **兼容性**：客户端和服务器的支持情况
- **性能需求**：是否需要多路复用和服务器推送
- **网络环境**：移动网络建议使用HTTP/3
- **实现复杂度**：HTTP/3的实现相对复杂

掌握这些协议的特性和实现方式，能够帮助开发者构建更高性能的Web应用。
